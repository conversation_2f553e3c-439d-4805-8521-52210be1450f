<?php

namespace Theme25;

/**
 * Централизиран клас за управление на конфигурационни данни от двете бази данни
 * Предоставя единен интерфейс за работа с конфигурации и заменя разпръснатата логика
 * 
 * @package Theme25
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class CommonMethods {

    /**
     * Singleton инстанция
     * @var CommonMethods|null
     */
    private static $instance = null;

    /**
     * Registry обект
     * @var \Registry
     */
    private $registry;

    /**
     * EnvLoader за достъп до .env настройки
     * @var EnvLoader
     */
    private $envLoader;

    /**
     * Кеш за конфигурационни стойности
     * @var array
     */
    private static $configCache = [];

    public $model_aliases = [];

    protected $_language = '';
    protected $_current_language = '';
    protected $_route = '';
    public $languages = [];
    public $active_language_id = 0;

    /**
     * Приватен конструктор за Singleton pattern
     */
    private function __construct() {
        // Инициализация ще се извърши в initialize()
    }

    /**
     * Получава singleton инстанцията
     * 
     * @param \Registry|null $registry Registry обект (задължителен при първо извикване)
     * @return CommonMethods
     * @throws \Exception Ако registry не е подаден при първо извикване
     */
    public static function getInstance($registry = null) {
        if (self::$instance === null) {
            if ($registry === null) {
                throw new \Exception('Registry is required for first initialization of CommonMethods');
            }
            
            self::$instance = new self();
            self::$instance->initialize($registry);
        }
        
        return self::$instance;
    }

    /**
     * Инициализира обекта с Registry
     * 
     * @param \Registry $registry
     */
    private function initialize($registry) {
        $this->registry = $registry;
        
        // Инициализация на EnvLoader
        $envPath = defined('DIR_THEME') ? DIR_THEME . '.env' : __DIR__ . '/.env';
        $this->envLoader = new EnvLoader($envPath);

        $this->prepareLanguageData();
    }


    public function getRegistry() {
        return $this->registry;
    }

    /**
     * Получава конфигурационна стойност с автоматично определяне на базата данни
     * Използва логиката от BaseProcessor за определяне на активната база данни
     * 
     * @param string $key Ключ на конфигурацията
     * @param mixed $default Стойност по подразбиране
     * @param string|null $route Път за определяне на базата данни (ако не е подаден, използва текущия контекст)
     * @return mixed Конфигурационна стойност
     */
    public function getConfig($key, $default = null, $route = null) {
        try {
            // Проверка в кеша
            $cacheKey = "auto_{$key}_{$route}";
            if (isset(self::$configCache[$cacheKey])) {
                return self::$configCache[$cacheKey];
            }

            // Определяне дали да използваме втората база данни
            $useSecondDb = $this->shouldUseSecondDb($route);
            
            if ($useSecondDb) {
                $value = $this->getConfigFromSecondDB($key, $default);
            } else {
                $value = $this->getConfigFromFirstDB($key, $default);
            }

            // Кеширане на резултата
            self::$configCache[$cacheKey] = $value;
            
            return $value;
            
        } catch (\Exception $e) {
            $this->logError("Error in getConfig for key '{$key}': " . $e->getMessage());
            return $default;
        }
    }

    /**
     * Получава конфигурационна стойност винаги от основната база данни
     * 
     * @param string $key Ключ на конфигурацията
     * @param mixed $default Стойност по подразбиране
     * @return mixed Конфигурационна стойност
     */
    public function getConfigFromFirstDB($key, $default = null, $code = 'config') {
        try {
            // Проверка в кеша
            $cacheKey = "first_{$key}";
            if (isset(self::$configCache[$cacheKey])) {
                return self::$configCache[$cacheKey];
            }

            // Проверка на registry
            if (!$this->registry) {
                $this->logError("ERROR getConfigFromFirstDB: Registry is null for key '{$key}'");
                return $default;
            }

            // Опит 1: Директно извличане от config обекта
            $config = $this->registry->get('config');

            

            if ($config && method_exists($config, 'get')) {
                $value = $config->get($key);

                if ($value !== null) {
                    $result = $value;
                    // Кеширане на резултата
                    self::$configCache[$cacheKey] = $result;
                    return $result;
                }
            }

            // Опит 2: Директна заявка към първата база данни
            try {
                // Получаване на първата база данни
                $firstDb = $this->getFirstDatabase();

                if ($firstDb) {
                    // Директна SQL заявка към първата база данни за config настройките
                    $sql = "SELECT * FROM " . DB_PREFIX . "setting WHERE store_id = '0' AND `code` = '{$code}'";
                    $query = $firstDb->query($sql);

                    if ($query && $query->rows) {
                        $configSettings = [];
                        foreach ($query->rows as $row) {
                            if (!$row['serialized']) {
                                $configSettings[$row['key']] = $row['value'];
                            } else {
                                $configSettings[$row['key']] = json_decode($row['value'], true);
                            }
                        }

                        if (isset($configSettings[$key])) {
                            $result = $configSettings[$key];
                            // Кеширане на резултата
                            self::$configCache[$cacheKey] = $result;
                            return $result;
                        }
                    }
                }
            } catch (\Exception $dbException) {
                $this->logError("WARNING getConfigFromFirstDB: Direct DB query failed for key '{$key}': " . $dbException->getMessage());
            }

            // Ако нищо не работи, връщаме default стойността
            $result = $default;

            // Кеширане на резултата (дори ако е default)
            self::$configCache[$cacheKey] = $result;

            return $result;

        } catch (\Exception $e) {
            $this->logError("ERROR in getConfigFromFirstDB for key '{$key}': " . $e->getMessage());
            return $default;
        }
    }

    /**
     * Получава конфигурационна стойност винаги от втората база данни
     * 
     * @param string $key Ключ на конфигурацията
     * @param mixed $default Стойност по подразбиране
     * @return mixed Конфигурационна стойност
     */
    public function getConfigFromSecondDB($key, $default = null, $code = 'config') {
        try {
            // Проверка в кеша
            $cacheKey = "second_{$key}";
            if (isset(self::$configCache[$cacheKey])) {
                return self::$configCache[$cacheKey];
            }

            // Проверка дали втората база данни е активна
            if (!$this->isSecondDBEnabled()) {
                return $this->getConfigFromFirstDB($key, $default, $code);
            }

            // Получаване на втората база данни
            $secondDb = $this->getSecondDatabase();
            if ($secondDb === null) {
                return $this->getConfigFromFirstDB($key, $default, $code);
            }

            // Запазване на текущата конфигурация
            $currentConfig = $this->getConfigFromFirstDB($key, null, $code);

            // Превключване към конфигурацията на втората база данни
            ConfigManager::switchToSecondDbConfig($this->registry, $secondDb);

            // Извличане на конфигурационната стойност от втората база данни
            $config = $this->registry->get('config');
            $secondDbConfig = $config ? $config->get($key) : null;

            // Превключване обратно към конфигурацията на първата база данни
            ConfigManager::switchToFirstDbConfig($this->registry);

            // Определяне на резултата
            $result = $secondDbConfig !== null ? $secondDbConfig : ($currentConfig !== null ? $currentConfig : $default);
            
            // Кеширане на резултата
            self::$configCache[$cacheKey] = $result;
            
            return $result;
            
        } catch (\Exception $e) {
            $this->logError("Error in getConfigFromSecondDB for key '{$key}': " . $e->getMessage());
            
            // Fallback към първата база данни
            return $this->getConfigFromFirstDB($key, $default, $code);
        }
    }

    /**
     * Записва конфигурационна стойност според активната база данни
     * 
     * @param string $key Ключ на конфигурацията
     * @param mixed $value Стойност за запис
     * @param string|null $route Път за определяне на базата данни
     * @return bool Успех на операцията
     */
    public function setConfig($key, $value, $route = null, $code = 'config') {
        try {
            // Изчистване на кеша за този ключ
            $this->clearConfigCache($key);
            
            // Определяне дали да използваме втората база данни
            $useSecondDb = $this->shouldUseSecondDb($route);
            
            if ($useSecondDb) {
                return $this->setConfigIntoSecondDB($key, $value, $code);
            } else {
                return $this->setConfigIntoFirstDB($key, $value, $code);
            }
            
        } catch (\Exception $e) {
            $this->logError("Error in setConfig for key '{$key}': " . $e->getMessage());
            return false;
        }
    }

    /**
     * Записва конфигурационна стойност винаги в основната база данни
     *
     * @param string $key Ключ на конфигурацията
     * @param mixed $value Стойност за запис
     * @return bool Успех на операцията
     */
    public function setConfigIntoFirstDB($key, $value, $code = 'config') {
        try {
            // Изчистване на кеша
            $this->clearConfigCache($key);

            // Получаване на първата база данни
            $firstDb = $this->getFirstDatabase();

            if (!$firstDb) {
                $this->logError("Error in setConfigIntoFirstDB: Cannot get first database for key '{$key}'");
                return false;
            }

            // Запазване на текущата база данни
            $originalDb = $this->registry->get('db');
            $currentConfig = $this->registry->get('config');

            try {
                // Гарантиране, че използваме първата база данни
                if ($firstDb !== $originalDb) {
                    $this->registry->set('db', $firstDb);
                    \Theme25\ConfigManager::switchToFirstDbConfig($this->registry);
                }

                // Използване на wrapper с първата база данни
                $settingWrapper = \Theme25\Model\OriginalSettingModelWrapper::getInstance($this->registry);

                // Определяне на code и store_id
                $code = 'config';
                $store_id = 0;

                // Запис на настройката
                $result = $settingWrapper->editSettingValue($code, $key, $value, $store_id);

                return $result;

            } finally {
                // Възстановяване на оригиналната база данни
                if ($firstDb !== $originalDb) {
                    $this->registry->set('db', $originalDb);
                    $this->registry->set('config', $currentConfig);
                }
            }

        } catch (\Exception $e) {
            $this->logError("Error in setConfigIntoFirstDB for key '{$key}': " . $e->getMessage());
            return false;
        }
    }

    /**
     * Записва конфигурационна стойност винаги във втората база данни
     * 
     * @param string $key Ключ на конфигурацията
     * @param mixed $value Стойност за запис
     * @return bool Успех на операцията
     */
    public function setConfigIntoSecondDB($key, $value, $code = 'config') {
        try {
            // Изчистване на кеша
            $this->clearConfigCache($key);
            
            // Проверка дали втората база данни е активна
            if (!$this->isSecondDBEnabled()) {
                return $this->setConfigIntoFirstDB($key, $value, $code);
            }

            // Получаване на втората база данни
            $secondDb = $this->getSecondDatabase();
            if ($secondDb === null) {
                return $this->setConfigIntoFirstDB($key, $value, $code);
            }

            // Запазване на оригиналната база данни
            $originalDb = $this->registry->get('db');
            
            // Превключване към втората база данни
            $this->registry->set('db', $secondDb);
            ConfigManager::switchToSecondDbConfig($this->registry, $secondDb);

            // Запис на настройката чрез singleton wrapper инстанцията
            $settingWrapper = \Theme25\Model\OriginalSettingModelWrapper::getInstance($this->registry);
            $result = $settingWrapper->editSettingValue($code, $key, $value, 0);

            // Възстановяване на оригиналната база данни
            $this->registry->set('db', $originalDb);
            ConfigManager::switchToFirstDbConfig($this->registry);
            
            return $result;
            
        } catch (\Exception $e) {
            $this->logError("Error in setConfigIntoSecondDB for key '{$key}': " . $e->getMessage());
            
            // Fallback към първата база данни
            return $this->setConfigIntoFirstDB($key, $value, $code);
        }
    }

    /**
     * Проверява дали даден път трябва да използва втората база данни
     * Използва логиката от BaseProcessor
     *
     * @param string|null $route Път за проверка
     * @return bool Дали пътят трябва да използва втората база данни
     */
    public function shouldUseSecondDb($route = null) {
        try {
            // Ако няма подаден route, опитваме се да го определим от текущия контекст
            if ($route === null) {
                $route = $this->getCurrentRoute();
            }

            if (empty($route)) {
                return false;
            }

            // Получаване на втората база данни
            $secondDb = $this->getSecondDatabase();
            if ($secondDb === null) {
                return false;
            }

            // Получаване на модулите за втората база данни
            $secondDbModules = $this->getSecondDbModules();

            foreach ($secondDbModules as $module) {
                if (strpos(strtolower($route), strtolower($module)) === 0) {
                    return true;
                }
            }

            return false;

        } catch (\Exception $e) {
            $this->logError("Error in shouldUseSecondDb for route '{$route}': " . $e->getMessage());
            return false;
        }
    }

    /**
     * Проверява дали втората база данни е активна
     *
     * @return bool
     */
    public function isSecondDBEnabled() {
        try {
            return $this->envLoader->get('SECOND_DB_ENABLED', false);
        } catch (\Exception $e) {
            $this->logError("Error checking if second DB is enabled: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Получава обекта за връзка с първата (основната) база данни
     * В OpenCart първата база данни е винаги оригиналната DB връзка
     *
     * @return \DB|null
     */
    public function getFirstDatabase() {
        try {
            // В OpenCart първата база данни е винаги оригиналната DB връзка
            // Тя се съхранява в registry под ключ 'db' при инициализация

            // Проверка дали имаме достъп до оригиналната база данни
            // Ако в момента сме превключени към втората база данни,
            // трябва да получим референция към оригиналната

            // Опит за получаване на оригиналната база данни от registry
            $originalDb = $this->registry->get('db');

            // Проверка дали текущата база данни е втората база данни
            $secondDb = $this->getSecondDatabase();

            if ($secondDb && $originalDb === $secondDb) {
                // Ако текущата база данни е втората, трябва да намерим оригиналната
                // В този случай ще създадем нова връзка с оригиналните настройки
                return $this->createOriginalDatabaseConnection();
            }

            return $originalDb;

        } catch (\Exception $e) {
            $this->logError("Error getting first database: " . $e->getMessage());
            // Fallback към текущата база данни
            return $this->registry->get('db');
        }
    }

    /**
     * Създава нова връзка с оригиналната база данни
     *
     * @return \DB|null
     */
    private function createOriginalDatabaseConnection() {
        try {
            // Използване на оригиналните константи за база данни
            $hostname = DB_HOSTNAME;
            $username = DB_USERNAME;
            $password = DB_PASSWORD;
            $database = DB_DATABASE;
            $port = defined('DB_PORT') ? DB_PORT : 3306;

            // Създаване на нова DB връзка
            $db = new \DB(DB_DRIVER, $hostname, $username, $password, $database, $port);

            return $db;

        } catch (\Exception $e) {
            $this->logError("Error creating original database connection: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Получава обекта за връзка с втората база данни
     *
     * @return \DB|null
     */
    public function getSecondDatabase() {
        try {
            // Използване на reflection за достъп до статичното свойство
            $reflection = new \ReflectionClass('\Theme25\BaseProcessor');
            $secondDbProperty = $reflection->getProperty('secondDb');
            $secondDbProperty->setAccessible(true);
            return $secondDbProperty->getValue();
        } catch (\Exception $e) {
            $this->logError("Error getting second database: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Получава списъка с модули за втората база данни
     *
     * @return array
     */
    public function getSecondDbModules() {
        try {
            // Използване на reflection за достъп до статичното свойство
            $reflection = new \ReflectionClass('\Theme25\BaseProcessor');
            $modulesProperty = $reflection->getProperty('secondDbModules');
            $modulesProperty->setAccessible(true);
            return $modulesProperty->getValue();
        } catch (\Exception $e) {
            $this->logError("Error getting second DB modules: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Опитва се да определи текущия route от контекста
     *
     * @return string|null
     */
    public function getCurrentRoute() {
        try {
            // Опит за получаване на route от request
            $request = $this->registry->get('request');
            if ($request && isset($request->get['route'])) {
                return $request->get['route'];
            }

            // Опит за получаване на route от session или други източници
            // Това може да се разшири според нуждите

            return null;
        } catch (\Exception $e) {
            $this->logError("Error getting current route: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Изчиства кеша за конфигурационни стойности
     *
     * @param string|null $key Конкретен ключ за изчистване (ако е null, изчиства целия кеш)
     */
    public function clearConfigCache($key = null) {
        if ($key === null) {
            self::$configCache = [];
        } else {
            // Изчистване на всички кеширани стойности за този ключ
            $keysToRemove = [];
            foreach (self::$configCache as $cacheKey => $value) {
                if (strpos($cacheKey, "_{$key}") !== false) {
                    $keysToRemove[] = $cacheKey;
                }
            }

            foreach ($keysToRemove as $keyToRemove) {
                unset(self::$configCache[$keyToRemove]);
            }
        }
    }

    /**
     * Записва грешка в лог файла
     *
     * @param string $message Съобщение за грешка
     */
    private function logError($message) {
        try {
            if (class_exists('\Log')) {
                $log = new \Log('common_methods_error.log');
                $log->write($message);
            }

            // Допълнително логване чрез F() ако е налично
            if (function_exists('F') && F()->log) {
                F()->log->error($message, __FILE__, __LINE__);
            }
        } catch (\Exception $e) {
            // Тихо игнориране на грешки при логване
        }
    }

    /**
     * Получава статистики за използването на кеша
     *
     * @return array
     */
    public function getCacheStats() {
        return [
            'total_cached_items' => count(self::$configCache),
            'cache_keys' => array_keys(self::$configCache),
            'memory_usage' => memory_get_usage(true)
        ];
    }

    /**
     * Записва множество конфигурационни стойности наведнъж в основната база данни
     * По-ефективно от множество отделни извиквания
     *
     * @param array $settings Асоциативен масив с ключ => стойност
     * @param string $code Код на настройките (по подразбиране 'config')
     * @param int $store_id Store ID (по подразбиране 0)
     * @return bool Успех на операцията
     */
    public function setMultipleConfigsIntoFirstDB($settings, $code = 'config', $store_id = 0) {
        try {
            // Изчистване на кеша за всички ключове
            foreach (array_keys($settings) as $key) {
                $this->clearConfigCache($key);
            }

            // Получаване на първата база данни
            $firstDb = $this->getFirstDatabase();

            if (!$firstDb) {
                $this->logError("Error in setMultipleConfigsIntoFirstDB: Cannot get first database");
                return false;
            }

            // Запазване на текущата база данни
            $originalDb = $this->registry->get('db');
            $currentConfig = $this->registry->get('config');

            $result = false;

            try {
                // Гарантиране, че използваме първата база данни
                if ($firstDb !== $originalDb) {
                    $this->registry->set('db', $firstDb);
                    \Theme25\ConfigManager::switchToFirstDbConfig($this->registry);
                }

                // Използване на wrapper с първата база данни
                $settingWrapper = \Theme25\Model\OriginalSettingModelWrapper::getInstance($this->registry);

                // Запис на всички настройки наведнъж
                $result = $settingWrapper->editSetting($code, $settings, $store_id);

                return $result;

            } finally {
                // Възстановяване на оригиналната база данни
                if ($firstDb !== $originalDb) {
                    $this->registry->set('db', $originalDb);
                    $this->registry->set('config', $currentConfig);
                }
            }

        } catch (\Exception $e) {
            $this->logError("Error in setMultipleConfigsIntoFirstDB: " . $e->getMessage());
            return false;
        }


    }

    /**
     * Записва множество конфигурационни стойности наведнъж във втората база данни
     *
     * @param array $settings Асоциативен масив с ключ => стойност
     * @param string $code Код на настройките (по подразбиране 'config')
     * @param int $store_id Store ID (по подразбиране 0)
     * @return bool Успех на операцията
     */
    public function setMultipleConfigsIntoSecondDB($settings, $code = 'config', $store_id = 0) {
        try {
            // Изчистване на кеша за всички ключове
            foreach (array_keys($settings) as $key) {
                $this->clearConfigCache($key);
            }

            // Проверка дали втората база данни е активна
            if (!$this->isSecondDBEnabled()) {
                return $this->setMultipleConfigsIntoFirstDB($settings, $code, $store_id);
            }

            // Получаване на втората база данни
            $secondDb = $this->getSecondDatabase();
            if ($secondDb === null) {
                return $this->setMultipleConfigsIntoFirstDB($settings, $code, $store_id);
            }

            // Запазване на оригиналната база данни
            $originalDb = $this->registry->get('db');

            // Превключване към втората база данни
            $this->registry->set('db', $secondDb);
            ConfigManager::switchToSecondDbConfig($this->registry, $secondDb);

            // Запис на настройките
            $settingWrapper = \Theme25\Model\OriginalSettingModelWrapper::getInstance($this->registry);
            $result = $settingWrapper->editSetting($code, $settings, $store_id);

            // Възстановяване на оригиналната база данни
            $this->registry->set('db', $originalDb);
            ConfigManager::switchToFirstDbConfig($this->registry);

            return $result;

        } catch (\Exception $e) {
            $this->logError("Error in setMultipleConfigsIntoSecondDB: " . $e->getMessage());

            // Fallback към първата база данни
            return $this->setMultipleConfigsIntoFirstDB($settings, $code, $store_id);
        }
    }

    /**
     * Получава всички настройки за даден код и store_id
     *
     * @param string $code Код на настройките (напр. 'config')
     * @param int $store_id Store ID (по подразбиране 0)
     * @param string|null $route Път за определяне на базата данни
     * @return array Масив с настройки
     */
    public function getAllSettings($code, $store_id = 0, $route = null) {
        try {
            // Проверка в кеша
            $cacheKey = "all_settings_{$code}_{$store_id}_{$route}";
            if (isset(self::$configCache[$cacheKey])) {
                return self::$configCache[$cacheKey];
            }

            // Определяне дали да използваме втората база данни
            $useSecondDb = $this->shouldUseSecondDb($route);

            if ($useSecondDb) {
                $result = $this->getAllSettingsFromSecondDB($code, $store_id);
            } else {
                $result = $this->getAllSettingsFromFirstDB($code, $store_id);
            }

            // Кеширане на резултата
            self::$configCache[$cacheKey] = $result;

            return $result;

        } catch (\Exception $e) {
            $this->logError("Error in getAllSettings for code '{$code}': " . $e->getMessage());
            return [];
        }
    }

    /**
     * Получава всички настройки винаги от основната база данни
     *
     * @param string $code Код на настройките
     * @param int $store_id Store ID
     * @return array
     */
    public function getAllSettingsFromFirstDB($code, $store_id = 0) {
        try {
            // Проверка в кеша
            $cacheKey = "all_settings_first_{$code}_{$store_id}";
            if (isset(self::$configCache[$cacheKey])) {
                return self::$configCache[$cacheKey];
            }

            // Проверка на registry
            if (!$this->registry) {
                $this->logError("ERROR getAllSettingsFromFirstDB: Registry is null for code '{$code}'");
                return [];
            }

            // Използване на singleton wrapper инстанцията
            $settingWrapper = \Theme25\Model\OriginalSettingModelWrapper::getInstance($this->registry);

            if (!$settingWrapper) {
                $this->logError("ERROR getAllSettingsFromFirstDB: SettingWrapper is null for code '{$code}'");
                return [];
            }

            // Извикване на getSetting метода
            $result = $settingWrapper->getSetting($code, $store_id);

            // Валидация на резултата
            if (!is_array($result)) {
                $this->logError("WARNING getAllSettingsFromFirstDB: getSetting returned non-array for code '{$code}': " . var_export($result, true));
                $result = [];
            }

            // Кеширане на резултата
            self::$configCache[$cacheKey] = $result;

            return $result;

        } catch (\Exception $e) {
            $this->logError("ERROR in getAllSettingsFromFirstDB for code '{$code}': " . $e->getMessage());
            return [];
        }
    }

    /**
     * Получава всички настройки винаги от втората база данни
     *
     * @param string $code Код на настройките
     * @param int $store_id Store ID
     * @return array
     */
    public function getAllSettingsFromSecondDB($code, $store_id = 0) {
        try {
            // Проверка в кеша
            $cacheKey = "all_settings_second_{$code}_{$store_id}";
            if (isset(self::$configCache[$cacheKey])) {
                return self::$configCache[$cacheKey];
            }

            // Проверка дали втората база данни е активна
            if (!$this->isSecondDBEnabled()) {
                return $this->getAllSettingsFromFirstDB($code, $store_id);
            }

            // Получаване на втората база данни
            $secondDb = $this->getSecondDatabase();
            if ($secondDb === null) {
                return $this->getAllSettingsFromFirstDB($code, $store_id);
            }

            // Запазване на оригиналната база данни
            $originalDb = $this->registry->get('db');

            // Превключване към втората база данни
            $this->registry->set('db', $secondDb);
            ConfigManager::switchToSecondDbConfig($this->registry, $secondDb);

            // Получаване на настройките
            $settingWrapper = \Theme25\Model\OriginalSettingModelWrapper::getInstance($this->registry);
            $result = $settingWrapper->getSetting($code, $store_id);

            // Възстановяване на оригиналната база данни
            $this->registry->set('db', $originalDb);
            ConfigManager::switchToFirstDbConfig($this->registry);

            // Кеширане на резултата
            self::$configCache[$cacheKey] = $result;

            return $result;

        } catch (\Exception $e) {
            $this->logError("Error in getAllSettingsFromSecondDB for code '{$code}': " . $e->getMessage());

            // Fallback към първата база данни
            return $this->getAllSettingsFromFirstDB($code, $store_id);
        }
    }

    /**
     * Тества връзката с първата база данни и връща диагностична информация
     *
     * @return array Диагностична информация
     */
    public function testFirstDBConnection() {
        $diagnostics = [
            'registry_available' => false,
            'config_available' => false,
            'db_available' => false,
            'wrapper_available' => false,
            'config_test' => null,
            'wrapper_test' => null,
            'errors' => []
        ];

        try {
            // Тест на registry
            if ($this->registry) {
                $diagnostics['registry_available'] = true;

                // Тест на config обект
                $config = $this->registry->get('config');
                if ($config) {
                    $diagnostics['config_available'] = true;

                    // Тест на config->get()
                    try {
                        $testValue = $config->get('config_name');
                        $diagnostics['config_test'] = $testValue;
                    } catch (\Exception $e) {
                        $diagnostics['errors'][] = 'Config->get() failed: ' . $e->getMessage();
                    }
                }

                // Тест на db обект
                $db = $this->registry->get('db');
                if ($db) {
                    $diagnostics['db_available'] = true;
                }

                // Тест на OriginalSettingModelWrapper
                try {
                    $wrapper = \Theme25\Model\OriginalSettingModelWrapper::getInstance($this->registry);
                    if ($wrapper) {
                        $diagnostics['wrapper_available'] = true;

                        // Тест на wrapper->getSetting()
                        $wrapperResult = $wrapper->getSetting('config', 0);
                        $diagnostics['wrapper_test'] = is_array($wrapperResult) ? count($wrapperResult) . ' settings' : var_export($wrapperResult, true);
                    }
                } catch (\Exception $e) {
                    $diagnostics['errors'][] = 'OriginalSettingModelWrapper failed: ' . $e->getMessage();
                }
            }

        } catch (\Exception $e) {
            $diagnostics['errors'][] = 'General error: ' . $e->getMessage();
        }

        return $diagnostics;
    }

    /**
     * Изтрива конфигурационна стойност с автоматично определяне на базата данни
     *
     * @param string $key Ключ на конфигурацията
     * @param string|null $route Път за определяне на базата данни
     * @return bool Успех на операцията
     */
    public function deleteConfig($key, $route = null) {
        try {
            // Изчистване на кеша
            $this->clearConfigCache($key);

            // Определяне дали да използваме втората база данни
            $useSecondDb = $this->shouldUseSecondDb($route);

            if ($useSecondDb) {
                return $this->deleteConfigFromSecondDB($key);
            } else {
                return $this->deleteConfigFromFirstDB($key);
            }

        } catch (\Exception $e) {
            $this->logError("Error in deleteConfig for key '{$key}': " . $e->getMessage());
            return false;
        }
    }

    /**
     * Изтрива конфигурационна стойност винаги от първата база данни
     *
     * @param string $key Ключ на конфигурацията
     * @return bool Успех на операцията
     */
    public function deleteConfigFromFirstDB($key) {
        try {
            // Изчистване на кеша
            $this->clearConfigCache($key);

            // Получаване на първата база данни
            $firstDb = $this->getFirstDatabase();

            if (!$firstDb) {
                $this->logError("Error in deleteConfigFromFirstDB: Cannot get first database for key '{$key}'");
                return false;
            }

            // Запазване на текущата база данни
            $originalDb = $this->registry->get('db');
            $currentConfig = $this->registry->get('config');

            try {
                // Гарантиране, че използваме първата база данни
                if ($firstDb !== $originalDb) {
                    $this->registry->set('db', $firstDb);
                    \Theme25\ConfigManager::switchToFirstDbConfig($this->registry);
                }

                // Директна SQL заявка за изтриване
                $sql = "DELETE FROM " . DB_PREFIX . "setting
                        WHERE store_id = '0'
                        AND `code` = 'config'
                        AND `key` = '" . $firstDb->escape($key) . "'";

                $firstDb->query($sql);

                return true;

            } finally {
                // Възстановяване на оригиналната база данни
                if ($firstDb !== $originalDb) {
                    $this->registry->set('db', $originalDb);
                    $this->registry->set('config', $currentConfig);
                }
            }

        } catch (\Exception $e) {
            $this->logError("Error in deleteConfigFromFirstDB for key '{$key}': " . $e->getMessage());
            return false;
        }
    }

    /**
     * Изтрива конфигурационна стойност винаги от втората база данни
     *
     * @param string $key Ключ на конфигурацията
     * @return bool Успех на операцията
     */
    public function deleteConfigFromSecondDB($key) {
        try {
            // Изчистване на кеша
            $this->clearConfigCache($key);

            // Проверка дали втората база данни е активна
            if (!$this->isSecondDBEnabled()) {
                return $this->deleteConfigFromFirstDB($key);
            }

            // Получаване на втората база данни
            $secondDb = $this->getSecondDatabase();
            if ($secondDb === null) {
                return $this->deleteConfigFromFirstDB($key);
            }

            // Запазване на оригиналната база данни
            $originalDb = $this->registry->get('db');

            // Превключване към втората база данни
            $this->registry->set('db', $secondDb);
            \Theme25\ConfigManager::switchToSecondDbConfig($this->registry, $secondDb);

            // Директна SQL заявка за изтриване
            $sql = "DELETE FROM " . DB_PREFIX . "setting
                    WHERE store_id = '0'
                    AND `code` = 'config'
                    AND `key` = '" . $secondDb->escape($key) . "'";

            $secondDb->query($sql);

            // Възстановяване на оригиналната база данни
            $this->registry->set('db', $originalDb);
            \Theme25\ConfigManager::switchToFirstDbConfig($this->registry);

            return true;

        } catch (\Exception $e) {
            $this->logError("Error in deleteConfigFromSecondDB for key '{$key}': " . $e->getMessage());

            // Fallback към първата база данни
            return $this->deleteConfigFromFirstDB($key);
        }
    }

    /**
     * Проверява дали конфигурационна стойност съществува с автоматично определяне на базата данни
     *
     * @param string $key Ключ на конфигурацията
     * @param string|null $route Път за определяне на базата данни
     * @return bool Дали конфигурацията съществува
     */
    public function configExists($key, $route = null) {
        try {
            // Определяне дали да използваме втората база данни
            $useSecondDb = $this->shouldUseSecondDb($route);

            if ($useSecondDb) {
                return $this->configExistsInSecondDB($key);
            } else {
                return $this->configExistsInFirstDB($key);
            }

        } catch (\Exception $e) {
            $this->logError("Error in configExists for key '{$key}': " . $e->getMessage());
            return false;
        }
    }

    /**
     * Проверява дали конфигурационна стойност съществува в първата база данни
     *
     * @param string $key Ключ на конфигурацията
     * @return bool Дали конфигурацията съществува
     */
    public function configExistsInFirstDB($key) {
        try {
            // Опит за получаване на стойността
            $value = $this->getConfigFromFirstDB($key, '__NOT_FOUND__');
            return $value !== '__NOT_FOUND__';

        } catch (\Exception $e) {
            $this->logError("Error in configExistsInFirstDB for key '{$key}': " . $e->getMessage());
            return false;
        }
    }

    /**
     * Проверява дали конфигурационна стойност съществува във втората база данни
     *
     * @param string $key Ключ на конфигурацията
     * @return bool Дали конфигурацията съществува
     */
    public function configExistsInSecondDB($key) {
        try {
            // Опит за получаване на стойността
            $value = $this->getConfigFromSecondDB($key, '__NOT_FOUND__');
            return $value !== '__NOT_FOUND__';

        } catch (\Exception $e) {
            $this->logError("Error in configExistsInSecondDB for key '{$key}': " . $e->getMessage());
            return false;
        }
    }

    /**
     * Статичен метод за бърз достъп до getConfig
     *
     * @param string $key
     * @param mixed $default
     * @param string|null $route
     * @return mixed
     */
    public static function config($key, $default = null, $route = null) {
        try {
            $instance = self::getInstance();
            return $instance->getConfig($key, $default, $route);
        } catch (\Exception $e) {
            return $default;
        }
    }


    // РЕНДИРАНЕ НА ШАБЛОНИ

    public function renderTemplate($template, $data = [], $controllerPath = 'Backend', $standalone = false) {
        $viewProcessor = new \Theme25\ViewProcessor($this->registry, $controllerPath);
        if($standalone) {
            $data['__STANDALONE__'] = true;
        }
        return $viewProcessor->process($template, $data);
    }

    public function renderPartialTemplate($template, $data = [], $controllerPath = 'Backend') {
        return $this->renderTemplate($template, $data, $controllerPath, $standalone = true);
    }

    // МОДЕЛИ

    public function loadModel($route)
    {
        $this->registry->get('load')->model($route);
    }

    public function loadModels($routes)
    {
        foreach ($routes as $route) {
            $this->registry->get('load')->model($route);
        }
    }

    /**
     * Зарежда модел и го прави достъпен чрез персонализирана променлива
     *
     * @param string $route Път до модела (напр. 'catalog/product')
     * @param string $alias Име на променливата, чрез която ще се достъпва модела (напр. 'productModel')
     * @return void
     */
    public function loadModelAs($route, $alias)
    {

        // Проверка дали името не съвпада с резервирани имена
        $reservedNames = ['request', 'session', 'config', 'load', 'response', 'url', 'user', 'language', 'document', 'currency'];
        if (in_array($alias, $reservedNames)) {
            // Получаване на backtrace за по-подробна информация
            $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3);
            $caller = isset($backtrace[1]) ? $backtrace[1] : [];
            $callerClass = isset($caller['class']) ? $caller['class'] : 'Unknown';
            $callerMethod = isset($caller['function']) ? $caller['function'] : 'Unknown';
            $callerFile = isset($caller['file']) ? $caller['file'] : 'Unknown';
            $callerLine = isset($caller['line']) ? $caller['line'] : 'Unknown';

            // Генериране на по-подробно съобщение за грешка
            $errorMessage = "Името '{$alias}' е резервирано и не може да се използва като псевдоним за модел.\n";
            $errorMessage .= "Извикано от: {$callerClass}::{$callerMethod} в {$callerFile} на ред {$callerLine}.\n";
            $errorMessage .= "Моля, използвайте друго име за псевдоним за модела '{$route}'.";

            trigger_error($errorMessage, E_USER_WARNING);
            return;
        }

        try {
            // Зареждане на модела
            $load = $this->registry->get('load');
            if ($load) {
                $load->model($route);
            } else {
                return;
            }


            //$this->load->model($route);
        } catch (\Exception $e) {
            F()->log->developer($e->getMessage(), __FILE__, __LINE__);
            return;
        }

        // Създаване на референция към модела с новото име
        $modelName = 'model_' . str_replace('/', '_', $route);
        $this->$alias = $this->$modelName;
    }

    /**
     * Зарежда множество модели и ги прави достъпни чрез персонализирани променливи
     *
     * @param array $modelMap Асоциативен масив с път до модела като ключ и име на променливата като стойност
     * @return void
     */
    public function loadModelsAs($modelMap)
    {
        foreach ($modelMap as $route => $alias) {
            $this->loadModelAs($route, $alias);
        }
    }

    public function hasModel($instance_name)
    {
        return !empty($this->$instance_name);
    }

    public function getModel($instance_name)
    {
        if (!$this->hasModel($instance_name)) {
            throw new \Exception("Model '$instance_name' not found");
        }
        return $this->$instance_name;
    }

    public function loadLanguage($route)
    {
        $this->load->language($route);
        $this->_current_language = $this->_route;
    }

    public function getLanguageText($key, $route = '')
    {
        if ($route)
            $this->loadLanguage($route);
        else
            $this->setDefaultLanguage();
        return $this->language->get($key);
    }

    public function setDefaultLanguage()
    {
        if ($this->_current_language != $this->_language && $this->_route) {
            $this->_language = $this->_route;
            $this->loadLanguage($this->_language);
        }
    }

    public function getLanguageId()
    {
        return $this->active_language_id;
    }

    protected function prepareLanguageData()
    {
        $this->loadModelAs('localisation/language', 'languageModel');
        $active_language_id = $this->getConfig('config_language_id');
        $active_language_id_sdb = $this->getConfigFromSecondDB('config_language_id');

        if (!empty($active_language_id_sdb)) {
            $active_language_id = $active_language_id_sdb;
        }

        $languages_data = $this->languageModel->getLanguages();
        $languages = [];
        $language_exists = false;

        foreach ($languages_data as $language) {
            $languages[] = [
                'language_id' => $language['language_id'],
                'code' => $language['code'],
                'name' => $language['name'],
                'css' => $language['code'] == 'bg-bg' ? ' bg-languagebg' : ' bg-languageen'
            ];
            if ($language['language_id'] == $active_language_id) {
                $language_exists = true;
            }
        }

        $this->languages = $languages;

        if (!$language_exists && !empty($languages)) {
            $active_language_id = $languages[0]['language_id'];
        }

        $this->active_language_id = $active_language_id;
    }

    /**
     * Предотвратява клониране на singleton инстанцията
     */
    private function __clone() {}

    /**
     * Предотвратява десериализация на singleton инстанцията
     */
    public function __wakeup() {
        throw new \Exception("Cannot unserialize singleton");
    }

    public function __get($key) {
		return $this->registry->get($key);
	}

	public function __set($key, $value) {
		$this->registry->set($key, $value);
	}



}

