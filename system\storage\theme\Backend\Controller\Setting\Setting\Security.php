<?php

namespace Theme25\Backend\Controller\Setting\Setting;

/**
 * Sub-контролер за настройки за сигурност
 *
 * Този контролер управлява логиката за показване и обработка на настройките за сигурност,
 * включително IP ограничения, двуфакторна автентикация и други мерки за сигурност.
 *
 * @package Theme25\Backend\Controller\Setting\Setting
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class Security extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);

        $this->loadModelAs('setting/security', 'securitySettings');
    }

    /**
     * Подготвя данните за настройките за сигурност
     */
    public function prepareData() {
        $this->prepareSecuritySettingsData()
             ->prepareIPRestrictionData()
             ->preparePasswordPolicyData()
             ->prepareLoginSecurityData()
             ->prepareUrlsAndActions()
             ->prepareValidationRules();
    }

    /**
     * Подготвя основните данни за настройките за сигурност
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareSecuritySettingsData() {
        try {
            
            $settings = $this->securitySettings->getSettings();
            
            // Добавяне на настройките към данните
            foreach ($settings as $key => $value) {
                $this->setData($key, $value);
            }
            
        } catch (Exception $e) {
            $this->setError('Грешка при зареждане на настройки за сигурност: ' . $e->getMessage());
            
            // Задаване на стойности по подразбиране
            $this->setData([
                'ip_restriction_enabled' => 0,
                'ip_whitelist' => '[]',
                'ip_blacklist' => '[]',
                'allowed_ips' => [],
                'password_complexity_enabled' => 1,
                'password_min_length' => 8,
                'password_require_uppercase' => 1,
                'password_require_lowercase' => 1,
                'password_require_numbers' => 1,
                'password_require_symbols' => 0,
                'password_history_enabled' => 1,
                'password_history_count' => 5,
                'password_expiry_enabled' => 0,
                'password_expiry_days' => 90,
                'security_logging_enabled' => 1,
                'failed_login_logging' => 1,
                'admin_action_logging' => 1,
                'max_login_attempts' => 5,
                'lockout_duration' => 30,
                'login_notifications' => 1
            ]);
        }

        return $this;
    }

    /**
     * Подготвя данните за IP ограничения
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareIPRestrictionData() {
        // Зареждане на IP данни от новите таблици (първите 20 за началната страница)
        $whitelist_ips = $this->securitySettings->getWhitelistIPsPaginated(1, 20);
        $blacklist_ips = $this->securitySettings->getBlacklistIPsPaginated(1, 20);

        // Получаване на общия брой записи за pagination
        $whitelist_total = $this->securitySettings->getTotalWhitelistIPs();
        $blacklist_total = $this->securitySettings->getTotalBlacklistIPs();


        // Проверка дали текущият IP е в whitelist
        $current_ip = $this->getCurrentIPAddress();
        $current_ip_in_whitelist = false;

        if (!empty($current_ip)) {
            // Проверка в заредените данни
            foreach ($whitelist_ips as $ip_data) {
                if ($ip_data['ip_address'] === $current_ip) {
                    $current_ip_in_whitelist = true;
                    break;
                }
            }

            // Ако не е намерен в първите 20, проверяваме в цялата таблица
            if (!$current_ip_in_whitelist) {
                $current_ip_in_whitelist = $this->securitySettings->isIPInWhitelist($current_ip);
            }
        }

        // Подготовка на данни за Twig шаблона
        $this->setData('whitelist_ips', $whitelist_ips);
        $this->setData('blacklist_ips', $blacklist_ips);
        $this->setData('whitelist_total', $whitelist_total);
        $this->setData('blacklist_total', $blacklist_total);
        $this->setData('current_ip_in_whitelist', $current_ip_in_whitelist);

        // Генериране на JSON структура за IP адреси
        $whitelist_json_data = [];
        if (!empty($whitelist_ips) && is_array($whitelist_ips)) {
            foreach ($whitelist_ips as $ip) {
                // Проверка дали IP адресът е валиден
                if (isset($ip['ip_address']) && !empty($ip['ip_address']) && $ip['ip_address'] !== '[]') {
                    $whitelist_json_data[] = [
                        'ip' => $ip['ip_address'],
                        'description' => $ip['description'] ?? '',
                        'status' => (int)($ip['status'] ?? 1),
                        'id' => $ip['id'] ?? null
                    ];
                }
            }
        }

        $blacklist_json_data = [];
        if (!empty($blacklist_ips) && is_array($blacklist_ips)) {
            foreach ($blacklist_ips as $ip) {
                // Проверка дали IP адресът е валиден
                if (isset($ip['ip_address']) && !empty($ip['ip_address']) && $ip['ip_address'] !== '[]') {
                    $blacklist_json_data[] = [
                        'ip' => $ip['ip_address'],
                        'description' => $ip['description'] ?? '',
                        'status' => (int)($ip['status'] ?? 1),
                        'id' => $ip['id'] ?? null
                    ];
                }
            }
        }

        // Задаване на JSON структурата за скритите полета (винаги валиден JSON)
        // Използваме JSON_UNESCAPED_UNICODE за директна кирилица вместо Unicode escape последователности
        $whitelist_json = $whitelist_json_data ? json_encode($whitelist_json_data, JSON_UNESCAPED_UNICODE) : '';
        $blacklist_json = $blacklist_json_data ? json_encode($blacklist_json_data, JSON_UNESCAPED_UNICODE) : '';

        $this->setData('ip_whitelist', $whitelist_json);
        $this->setData('ip_blacklist', $blacklist_json);

        // Backward compatibility - създаване на legacy формат
        $ip_whitelist_string = implode(',', array_column($whitelist_ips, 'ip_address'));
        $this->setData('allowed_ips_text', $ip_whitelist_string);

        // Подготовка на масив с активни IP адреси
        $allowed_ips = array_column($whitelist_ips, 'ip_address');
        $this->setData('allowed_ips', $allowed_ips);

        // Задаване на ip_restriction_enabled от настройките
        $ip_restriction_enabled = $this->getData('ip_restriction_enabled') ? $this->getData('ip_restriction_enabled') : 0;
        $this->setData('ip_restriction_enabled', $ip_restriction_enabled);

        // Получаване на текущия IP адрес
        $current_ip = $this->getCurrentIPAddress();
        $this->setData('current_ip', $current_ip);

        // Проверка дали текущият IP е в whitelist
        $is_current_ip_allowed = $this->securitySettings->isIPInWhitelist($current_ip);
        $this->setData('is_current_ip_allowed', $is_current_ip_allowed);

        return $this;
    }

    /**
     * Получава текущия IP адрес с поддръжка за proxy
     *
     * @return string
     */
    private function getCurrentIPAddress() {
        // Проверка за различни HTTP headers за реален IP
        // $ip_headers = [
        //     'HTTP_CF_CONNECTING_IP',     // Cloudflare
        //     'HTTP_CLIENT_IP',            // Proxy
        //     'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
        //     'HTTP_X_FORWARDED',          // Proxy
        //     'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
        //     'HTTP_FORWARDED_FOR',        // Proxy
        //     'HTTP_FORWARDED',            // Proxy
        //     'REMOTE_ADDR'                // Standard
        // ];

        // foreach ($ip_headers as $header) {
        //     if (!empty($this->request->server[$header])) {
        //         $ip = $this->request->server[$header];

        //         // Ако има множество IP адреси (разделени със запетая), вземи първия
        //         if (strpos($ip, ',') !== false) {
        //             $ip = trim(explode(',', $ip)[0]);
        //         }

        //         // Валидация на IP адреса
        //         if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
        //             return $ip;
        //         }
        //     }
        // }

        // Fallback към REMOTE_ADDR дори ако е частен IP
        return $this->request->server['REMOTE_ADDR'] ? $this->request->server['REMOTE_ADDR'] : 'unknown';
    }

    /**
     * Подготвя данните за сигурност при вход
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareLoginSecurityData() {
        // Опции за максимални опити за вход
        $max_login_attempts_options = [
            3 => '3 опита',
            5 => '5 опита',
            10 => '10 опита',
            15 => '15 опита',
            20 => '20 опита'
        ];

        $this->setData('max_login_attempts_options', $max_login_attempts_options);

        // Опции за време на заключване
        $lockout_duration_options = [
            5 => '5 минути',
            10 => '10 минути',
            15 => '15 минути',
            30 => '30 минути',
            60 => '1 час',
            120 => '2 часа',
            1440 => '24 часа'
        ];

        $this->setData('lockout_duration_options', $lockout_duration_options);

        return $this;
    }

    /**
     * Подготвя данните за политика за пароли
     *
     * @return $this За верижно извикване на методи
     */
    private function preparePasswordPolicyData() {
        // Опции за минимална дължина на паролата
        $password_length_options = [];
        for ($i = 6; $i <= 32; $i++) {
            $password_length_options[$i] = $i . ' символа';
        }

        $this->setData('password_length_options', $password_length_options);

        // Опции за брой пароли в историята
        $password_history_options = [];
        for ($i = 1; $i <= 10; $i++) {
            $password_history_options[$i] = $i . ' пароли';
        }

        $this->setData('password_history_options', $password_history_options);

        // Опции за дни до изтичане на паролата
        $password_expiry_options = [
            30 => '30 дни',
            60 => '60 дни',
            90 => '90 дни',
            180 => '180 дни',
            365 => '365 дни'
        ];

        $this->setData('password_expiry_options', $password_expiry_options);

        return $this;
    }

    /**
     * Подготвя URL-и за AJAX заявки и действия
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareUrlsAndActions() {
        $urls = [
            'save_url' => $this->getAdminLink('setting/setting/security_save'),
            'test_ip_url' => $this->getAdminLink('setting/setting/test_ip_restriction'),
            'clear_failed_logins_url' => $this->getAdminLink('setting/setting/clear_failed_logins'),
            'export_activity_log_url' => $this->getAdminLink('setting/setting/export_activity_log')
        ];

        foreach ($urls as $key => $url) {
            $this->setData($key, $url);
        }

        return $this;
    }

    /**
     * Подготвя правила за валидация
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareValidationRules() {
        $validation_rules = [
            'allowed_ips_text' => [
                'required' => false,
                'type' => 'ip_list'
            ],
            'ip_whitelist' => [
                'required' => false,
                'type' => 'ip_list'
            ],
            'ip_blacklist' => [
                'required' => false,
                'type' => 'ip_list'
            ],
            'password_min_length' => [
                'required' => true,
                'type' => 'number',
                'min' => 6,
                'max' => 32
            ],
            'password_history_count' => [
                'required' => true,
                'type' => 'number',
                'min' => 1,
                'max' => 50
            ],
            'password_expiry_days' => [
                'required' => true,
                'type' => 'number',
                'min' => 1,
                'max' => 365
            ],
            'max_login_attempts' => [
                'required' => true,
                'type' => 'number',
                'min' => 3,
                'max' => 20
            ],
            'lockout_duration' => [
                'required' => true,
                'type' => 'number',
                'min' => 5,
                'max' => 1440
            ]
        ];

        $this->setData('validation_rules', $validation_rules);

        return $this;
    }

    /**
     * Получава конфигурацията за JavaScript модула
     *
     * @return array
     */
    public function getJavaScriptConfig() {
        return [
            'userToken' => $this->getUserToken(),
            'currentIP' => $this->getData('current_ip'),
            'urls' => [
                'save' => $this->getData('save_url'),
                'testIP' => $this->getData('test_ip_url'),
                'clearFailedLogins' => $this->getData('clear_failed_logins_url'),
                'exportActivityLog' => $this->getData('export_activity_log_url')
            ],
            'validationRules' => $this->getData('validation_rules'),
        ];
    }
}
