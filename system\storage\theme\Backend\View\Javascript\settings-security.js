2025-08-09 10:14:42.167 [info] 'AugmentConfigListener' settings parsed successfully
2025-08-09 10:14:42.167 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":"# 📘 Правила за работа на AI агента\n\n## 🗣️ Език\n1. **Винаги отговаряй на български език.**\n\n## 💻 Основни технологии\n2. **Основният език за програмиране е PHP.** Кодът, който се пише, трябва да е съвместим с php 7.x.\n3. **При работа с JavaScript използвай само чист (vanilla) JavaScript**, освен ако не е изрично поискано друго.\n4. **Операционната система на файловете е Windows 10.**\n5. **Работа с MySQL се извършва чрез PHPMyAdmin.**\n\n## 🛠️ Работа с код и промени\n6. **При изпълнение на задача за добавяне или промяна на код:**\n   - Не редактирай, премахвай или подобрявай други части на кода, освен ако не е изрично указано.\n   - Ако откриеш възможна грешка или подобрение, предложи го отделно, **без да го прилагаш в работния код.**\n   - Винаги използвай is_callable вместо method_exists\n\n7. **Винаги използвай директна кирилица.** Не използвай Unicode escape формати (напр. `\\u041f` е грешно).\n\n## 🗄️ Работа с бази данни\n\n### 📌 Общи насоки:\n- Използвай **нови редове** за по-добра четимост на SQL кода.\n- Използвай **кавички** за SQL заявки и синтаксис `{$variable}` за променливи.\n\n### 📌 WordPress:\n- Използвай **вградената функционалност (ВФ)** на WordPress за заявки към базата данни.\n- За плъгини използвай `{{prefix}}` като префикс на таблиците.\n- Използвай директно кодиране на стойностите (не използвай escape с функции от типа на `addslashes` и т.н., ако ВФ го поддържа сама).\n\n### 📌 OpenCart:\n- Използвай константата `DB_PREFIX` за префикс на таблиците.\n\n## 🧩 Организация на кода\n9. **При генериране на по-сложна логика, я разбивай на подметоди (функции/методи)** за по-добра четимост и поддръжка.\n10. **Ако файлът е голям, прави промените на по-малки стъпки**, заради ограничения в размера на файловете.\n11. **Следвай принципа DRY (Don't Repeat Yourself).**\n12. **Придържай се към добрите практики в разработката** – цел: поддържан, стабилен и четим код.\n13. **Използвай съкратения синтаксис за масиви:**\n    ```php
\n    // Добре:\n    $data = ['key' => 'valaue'];\n\n    // Не:\n    $data = array('key' => 'value');\n
```\n\n## 🛡️ Архивиране и документация\n14. **ВИНАГИ преди да промениш даден файл, създай резервно копие**, като в името му добавиш дата и час (напр. `filename_2025-07-17_1530.php`). Винаги използвай PowerShell командите за копиране.\n15. **Ако в основната директория има файл `instrukcii-i-pravila.md`, прочети го.**\n16. **Ако в основната директория има файл `project-conventions.md`, прочети го**, за да разбереш структурата и конвенциите на проекта.\n\n## 🧪 Среда за работа и тестване\n17. **Не използвай локалната машина с Windows 10 за тестване.** На нея няма инсталирана подходяща платформа.\n\n## 📋 Организация на задачите\n18. **Винаги организирай работата си чрез списък със задачи (task-ове)** – за яснота и за да не се пропусне нищо.\n\n## Работа със файлове в Terminal\n19. Когато работиш с файлове в Terminal, винаги използвай PowerShell команди."},"agent":{},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"disableFocusOnAugmentPanel":false,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":true,"showDiffInHover":true,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-08-09 10:14:42.167 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"notificationPollingIntervalMs":180000,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"enableViewedContentTracking":false,"viewedContentCloseRangeThreshold":5,"viewedContentDiscreteJumpThreshold":15,"viewedContentMinEventAgeMs":1000,"viewedContentMaxEventAgeMs":3600000,"viewedContentMaxTrackedFiles":10,"viewedContentMaxSameFileEntries":2,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","enableNewThreadsList":false,"enableUntruncatedContentStorage":false,"maxLinesTerminalProcessOutput":0,"truncationFooterAdditionText":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","vscodeMinVersion":"1.96.0","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"agentChatModel":"","enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"modelInfoRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"enableAgentSwarmMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5,"historySummaryMinVersion":"","historySummaryParams":"","enableCommitIndexing":false,"maxCommitsToIndex":0,"enableExchangeStorage":false,"conversationHistorySizeThresholdBytes":268435456,"enableToolUseStateStorage":false,"retryChatStreamTimeouts":false,"remoteAgentCurrentWorkspace":false,"enableMemoryRetrieval":false,"enableAgentTabs":false,"enableSwarmMode":false,"enableGroupedTools":false,"remoteAgentsResumeHintAvailableTtlDays":0,"enableParallelTools":false,"enableAgentGitTracker":false,"enableNativeRemoteMcp":true,"vscodeTerminalStrategy":"vscode_events","enableSentry":false,"webviewErrorSamplingRate":0,"webviewTraceSamplingRate":0,"agentViewToolParams":""}
2025-08-09 10:14:42.167 [info] 'AugmentExtension' Retrieving model config
2025-08-09 10:14:42.486 [info] 'AugmentExtension' Retrieved model config
2025-08-09 10:14:42.486 [info] 'AugmentExtension' Returning model config
/**
 * JavaScript модул за настройки за сигурност
 * Следва BackendModule pattern на темата Rakla.bg
 * Разширява основния settings модул
 */
(function() {
    'use strict';

    // Функция за инициализация на security модула
    function initSecurityModule() {
        if (typeof BackendModule !== 'undefined' && typeof BackendModule === 'object') {
            // Добавяне на функционалност към основния модул
            extendBackendModuleWithSecurity();

            // Инициализиране на pagination за IP таблиците
            window.whitelistPagination = new IPTablePagination('whitelist');
            window.blacklistPagination = new IPTablePagination('blacklist');
        } else {
            console.error('BackendModule not found');
        }
    }

    // Разширяване на основния модул с функционалности за сигурност
    function extendBackendModuleWithSecurity() {
        Object.assign(BackendModule, {
            /**
             * Инициализиране на настройки за сигурност
             */
            initSecuritySettings: function() {
                this.initConfig();
                this.initIPAddressesTable();
                this.bindIPWhitelistToggle();
                this.logDev && this.logDev('Security settings module initialized');
            },

            initConfig: function() {
                this.settings.config.currentIP = window.settingsSecurityConfig?.currentIP || '';
            },


            /**
             * Обработка на click събития за security таб
             */
            handleSecurityClick: function(e) {
                const target = e.target;
                const self = this;

                const button = target.localName === 'button' ? target : target.closest('button');

                if (!button) return;

                if (button.id === 'save-security-settings') {
                    e.preventDefault();
                    self.saveSecuritySettings();

                } else if (button.id === 'clear-failed-logins') {
                    e.preventDefault();
                    self.clearFailedLogins();
                } else if (button.id === 'add-ip-address') {
                    e.preventDefault();
                    self.showAddIPModal();
                } else if (button.id === 'add-blacklist-ip') {
                    e.preventDefault();
                    self.showAddBlacklistIPModal();
                } else if (button.classList.contains('edit-ip')) {
                    e.preventDefault();
                    self.editIPAddress(button.dataset.ip);
                } else if (button.classList.contains('delete-ip')) {
                    e.preventDefault();
                    self.deleteIPAddress(button.dataset.ip);
                } else if (button.classList.contains('delete-blacklist-ip')) {
                    e.preventDefault();
                    self.deleteBlacklistIPAddress(button.dataset.ip);
                } else if (button.classList.contains('toggle-ip-status')) {
                    e.preventDefault();
                    self.toggleIPStatus(button.dataset.ip);
                }
            },

            /**
             * Обработка на form submit събития за security таб
             */
            handleSecurityFormSubmit: function(e) {
                e.preventDefault();
                const form = e.target;
                const self = this;

                if (form.id === 'security-settings-form') {
                    self.saveSecuritySettings();
                } else if (form.id === 'add-ip-form') {
                    self.submitAddIPForm(form);
                }
            },

            /**
             * Обработка на change събития за security таб
             */
            handleSecurityChange: function(e) {
                const target = e.target;
                const self = this;

                if (target.id === 'ip_restriction_enabled') {
                    self.toggleIPRestrictionFields(target.checked);
                }

                // Автоматично запазване при промяна на checkbox-и
                if (target.type === 'checkbox' && target.name && target.name.includes('setting')) {
                    self.autoSaveSettings();
                }
            },

            /**
             * Запазване на настройки за сигурност
             */
            saveSecuritySettings: function() {
                const self = this;
                const form = document.getElementById('security-settings-form');

                if (!form) {
                    self.showSettingsNotification('Формата не е намерена', 'error');
                    return;
                }

                // Валидация преди запазване
                const validationErrors = self.validateSecurityForm(form);
                if (validationErrors.length > 0) {
                    validationErrors.forEach(error => {
                        self.showSettingsNotification(error, 'error');
                    });
                    return;
                }

                

                const formData = new FormData(form);

                // Премахване на IP полетата от FormData - те се управляват чрез отделни AJAX заявки
                // Това предотвратява загубата на данни при пагинация (ако са заредени само 20 от 100+ IP адреса)
                formData.delete('ip_whitelist');
                formData.delete('ip_blacklist');

                // Debug лог за потвърждение
                console.log('FormData fields after removing IP lists:', Array.from(formData.keys()));

                // formData.append('user_token', self.settings.config.userToken);

                // Показване на loading състояние
                const saveButton = document.getElementById('save-security-settings');
                if (saveButton) {
                    saveButton.disabled = true;
                    saveButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }

                const ajaxUrl = self.settings.config.ajaxUrls.security_update;

                fetch(ajaxUrl, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Актуализиране на данните ако са предоставени
                        // if (data.updated_data) {
                        //     self.updateSecuritySettingsDisplay(data.updated_data);
                        // }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');

                        // Показване на грешки при валидация
                        if (data.validation_errors) {
                            self.showValidationErrors(data.validation_errors);
                        }
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error saving security settings:', error);
                    self.showSettingsNotification('Възникна грешка при запазването', 'error');
                })
                .finally(() => {
                    // Възстановяване на бутона
                    if (saveButton) {
                        saveButton.disabled = false;
                        saveButton.innerHTML = '<i class="ri-save-line mr-2"></i>Запази';
                    }
                });
            },

            /**
             * Валидация на формата за сигурност
             */
            validateSecurityForm: function(form) {
                const errors = [];

                // Валидация на минимална дължина на парола
                const minPasswordLength = form.querySelector('[name="security_password_min_length"]')?.value;
                if (minPasswordLength && (parseInt(minPasswordLength) < 6 || parseInt(minPasswordLength) > 32)) {
                    errors.push('Минималната дължина на паролата трябва да бъде между 6 и 32 символа');
                }

                // Валидация на максимални опити за вход
                const maxLoginAttempts = form.querySelector('[name="security_max_login_attempts"]')?.value;
                if (maxLoginAttempts && (parseInt(maxLoginAttempts) < 3 || parseInt(maxLoginAttempts) > 20)) {
                    errors.push('Максималните опити за вход трябва да бъдат между 3 и 20');
                }

                // Валидация на timeout на сесия
                const sessionTimeout = form.querySelector('[name="security_session_timeout"]')?.value;
                if (sessionTimeout && (parseInt(sessionTimeout) < 300 || parseInt(sessionTimeout) > 86400)) {
                    errors.push('Timeout на сесията трябва да бъде между 300 и 86400 секунди');
                }

                // Валидация на IP whitelist ако е активиран
                const ipRestrictionEnabled = form.querySelector('[name="ip_restriction_enabled"]')?.checked;
                const ipWhitelistJson = form.querySelector('[name="ip_whitelist"]')?.value;
                const ipBlacklistJson = form.querySelector('[name="ip_blacklist"]')?.value;

                // Парсиране на JSON данните
                let whitelistData = [];
                let blacklistData = [];
                let activeWhitelistIPs = [];

                // Debug логове за whitelist JSON
                console.log('Raw whitelist JSON string:', ipWhitelistJson);
                console.log('JSON string length:', ipWhitelistJson ? ipWhitelistJson.length : 'null/undefined');
                console.log('First 100 characters:', ipWhitelistJson ? ipWhitelistJson.substring(0, 100) : 'null/undefined');

                try {
                    // PHP контролерът винаги генерира валиден JSON масив ([] или [...])
                    if (!ipWhitelistJson || ipWhitelistJson.trim() === '') {
                        whitelistData = [];
                        console.log('Whitelist JSON is empty, using empty array');
                    } else {
                        // Почистване на потенциални проблемни символи
                        let cleanJson = ipWhitelistJson.trim();

                        // Проверка дали започва и завършва правилно
                        if (!cleanJson.startsWith('[') || !cleanJson.endsWith(']')) {
                            console.error('Invalid JSON format - does not start with [ or end with ]');
                            console.error('Received JSON:', cleanJson);
                            throw new Error('Invalid JSON format');
                        }

                        whitelistData = JSON.parse(cleanJson);
                        console.log('Whitelist JSON data parsed successfully:', whitelistData);

                        // Извличане на активните IP адреси с по-гъвкава проверка на статуса
                        activeWhitelistIPs = whitelistData
                            .filter(item => {
                                // Проверка на различни варианти на статуса
                                const isActive = item.status === 1 ||
                                               item.status === "1" ||
                                               parseInt(item.status) === 1;

                                const hasValidIP = item.ip &&
                                                 typeof item.ip === 'string' &&
                                                 item.ip.trim() !== '' &&
                                                 item.ip.trim() !== '[]';

                                console.log(`IP: ${item.ip}, Status: ${item.status} (${typeof item.status}), Active: ${isActive}, Valid IP: ${hasValidIP}`);

                                return isActive && hasValidIP;
                            })
                            .map(item => item.ip.trim());

                        console.log('Active whitelist IPs:', activeWhitelistIPs);
                    }
                } catch (e) {
                    console.error('JSON parse error for whitelist:', e);
                    console.error('Error message:', e.message);
                    console.error('Problematic JSON string:', ipWhitelistJson);

                    // Опит за автоматично поправяне на празен или некоректен JSON
                    if (!ipWhitelistJson || ipWhitelistJson.trim() === '' || ipWhitelistJson.trim() === '[]') {
                        whitelistData = [];
                        console.log('Using empty array for whitelist due to parse error');
                    } else {
                        errors.push(`Невалиден JSON формат за whitelist IP адреси: ${e.message}`);
                        whitelistData = []; // Fallback към празен масив
                    }
                }

                // Debug логове за blacklist JSON
                console.log('Raw blacklist JSON string:', ipBlacklistJson);
                console.log('Blacklist JSON string length:', ipBlacklistJson ? ipBlacklistJson.length : 'null/undefined');

                try {
                    // PHP контролерът винаги генерира валиден JSON масив ([] или [...])
                    if (!ipBlacklistJson || ipBlacklistJson.trim() === '') {
                        blacklistData = [];
                        console.log('Blacklist JSON is empty, using empty array');
                    } else {
                        // Почистване на потенциални проблемни символи
                        let cleanJson = ipBlacklistJson.trim();

                        // Проверка дали започва и завършва правилно
                        if (!cleanJson.startsWith('[') || !cleanJson.endsWith(']')) {
                            console.error('Invalid blacklist JSON format - does not start with [ or end with ]');
                            console.error('Received blacklist JSON:', cleanJson);
                            throw new Error('Invalid JSON format');
                        }

                        blacklistData = JSON.parse(cleanJson);
                        console.log('Blacklist JSON data parsed successfully:', blacklistData);
                    }
                } catch (e) {
                    console.error('JSON parse error for blacklist:', e);
                    console.error('Blacklist error message:', e.message);
                    console.error('Problematic blacklist JSON string:', ipBlacklistJson);

                    // Опит за автоматично поправяне на празен или некоректен JSON
                    if (!ipBlacklistJson || ipBlacklistJson.trim() === '' || ipBlacklistJson.trim() === '[]') {
                        blacklistData = [];
                        console.log('Using empty array for blacklist due to parse error');
                    } else {
                        errors.push(`Невалиден JSON формат за blacklist IP адреси: ${e.message}`);
                        blacklistData = []; // Fallback към празен масив
                    }
                }

                // Проверка дали има активни whitelist IP адреси когато ограниченията са включени
                console.log('IP restriction enabled:', ipRestrictionEnabled);
                console.log('Active whitelist IPs count:', activeWhitelistIPs.length);

                if (ipRestrictionEnabled && activeWhitelistIPs.length === 0) {
                    // Допълнителна проверка - може би JSON полето е празно или "[]"
                    if (!ipWhitelistJson || ipWhitelistJson.trim() === '' || ipWhitelistJson.trim() === '[]') {
                        errors.push('IP ограниченията са активирани, но няма зададени разрешени IP адреси');
                    } else {
                        errors.push('IP ограниченията са активирани, но няма активни разрешени IP адреси (проверете статуса на IP адресите)');
                    }
                }

                // Валидация на whitelist IP адреси
                if (whitelistData.length > 0) {
                    whitelistData.forEach((item, index) => {
                        if (item.ip &&
                            typeof item.ip === 'string' &&
                            item.ip.trim() !== '' &&
                            item.ip.trim() !== '[]') {
                            const ip = item.ip.trim();
                            if (!this.isValidIP(ip) && !this.isValidCIDR(ip)) {
                                errors.push(`Невалиден IP адрес в whitelist (ред ${index + 1}): ${ip}`);
                            }
                        }
                    });
                }

                // Валидация на blacklist IP адреси
                if (blacklistData.length > 0) {
                    blacklistData.forEach((item, index) => {
                        if (item.ip &&
                            typeof item.ip === 'string' &&
                            item.ip.trim() !== '' &&
                            item.ip.trim() !== '[]') {
                            const ip = item.ip.trim();
                            if (!this.isValidIP(ip) && !this.isValidCIDR(ip)) {
                                errors.push(`Невалиден IP адрес в blacklist (ред ${index + 1}): ${ip}`);
                            }
                        }
                    });
                }

                return errors;
            },

            /**
             * Управление на съобщенията за празни таблици
             */
            toggleEmptyTableMessages: function() {
                const whitelistTableBody = document.getElementById('whitelist-ip-addresses-table');
                const blacklistTableBody = document.getElementById('blacklist-ip-addresses-table');
                const whitelistEmptyMessage = document.getElementById('whitelist-no-ip-addresses');
                const blacklistEmptyMessage = document.getElementById('blacklist-no-ip-addresses');

                // Whitelist таблица
                if (whitelistTableBody && whitelistEmptyMessage) {
                    const hasWhitelistRows = whitelistTableBody.children.length > 0 &&
                                           Array.from(whitelistTableBody.children).some(row =>
                                               row.id !== 'whitelist-no-ip-addresses' &&
                                               row.dataset.ip &&
                                               row.dataset.ip !== '[]'
                                           );

                    if (hasWhitelistRows) {
                        whitelistEmptyMessage.style.display = 'none';
                    } else {
                        whitelistEmptyMessage.style.display = 'table-row';
                    }
                }

                // Blacklist таблица
                if (blacklistTableBody && blacklistEmptyMessage) {
                    const hasBlacklistRows = blacklistTableBody.children.length > 0 &&
                                           Array.from(blacklistTableBody.children).some(row =>
                                               row.dataset.ip &&
                                               row.dataset.ip !== '[]'
                                           );

                    if (hasBlacklistRows) {
                        blacklistEmptyMessage.classList.add('hidden');
                    } else {
                        blacklistEmptyMessage.classList.remove('hidden');
                    }
                }

                // Проверка за текущия IP в whitelist
                this.toggleCurrentIPButton();
            },

            /**
             * Показване/скриване на бутона "Добави текущия IP"
             */
            toggleCurrentIPButton: function() {
                const currentIP = window.settingsSecurityConfig?.currentIP || window.settingsConfig?.currentIP || '';
                const addCurrentIPBtn = document.getElementById('add-current-ip-btn');

                if (!currentIP || !addCurrentIPBtn) return;

                // Проверка дали текущият IP е в whitelist таблицата
                const whitelistTableBody = document.getElementById('whitelist-ip-addresses-table');
                let currentIPInWhitelist = false;

                if (whitelistTableBody) {
                    const rows = Array.from(whitelistTableBody.children);
                    currentIPInWhitelist = rows.some(row =>
                        row.dataset.ip === currentIP && row.id !== 'whitelist-no-ip-addresses'
                    );
                }

                // Показване/скриване на бутона
                if (currentIPInWhitelist) {
                    addCurrentIPBtn.classList.add('hidden');
                } else {
                    addCurrentIPBtn.classList.remove('hidden');
                }
            },

            /**
             * Проверка за валиден IP адрес
             */
            isValidIP: function(ip) {
                const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
                return ipRegex.test(ip);
            },

            /**
             * Проверка за валидна CIDR нотация
             */
            isValidCIDR: function(cidr) {
                const cidrRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\/([0-9]|[1-2][0-9]|3[0-2])$/;
                return cidrRegex.test(cidr);
            },

            /**
             * Toggle на IP restriction полета
             */
            toggleIPRestrictionFields: function(enabled) {
                const ipFields = document.getElementById('ip-restriction-fields');
                if (ipFields) {
                    if (enabled) {
                        ipFields.classList.remove('hidden');
                        ipFields.style.display = 'block';
                    } else {
                        ipFields.classList.add('hidden');
                        ipFields.style.display = 'none';
                    }
                }
            },



            /**
             * Изчистване на неуспешни опити за вход
             */
            clearFailedLogins: function() {
                const self = this;
                
                if (!confirm('Сигурни ли сте, че искате да изчистите всички неуспешни опити за вход?')) {
                    return;
                }

                fetch(self.settings.config.urls?.clearFailedLogins || self.settings.config.ajaxUrls?.clear_failed_logins || '', {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        user_token: self.settings.config.userToken
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.message || 'Неуспешните опити за вход са изчистени', 'success');
                        
                        // Актуализиране на статистиките
                        const failedLoginsCount = document.getElementById('failed-logins-count');
                        if (failedLoginsCount) {
                            failedLoginsCount.textContent = '0';
                        }
                    } else {
                        self.showSettingsNotification(data.error || 'Грешка при изчистването', 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error clearing failed logins:', error);
                    self.showSettingsNotification('Възникна грешка при изчистването', 'error');
                });
            },

            /**
             * Инициализиране на таблицата с IP адреси
             */
            initIPAddressesTable: function() {
                const self = this;

                // Инициализация на whitelist таблицата
                const whitelistTableBody = document.getElementById('whitelist-ip-addresses-table');
                if (whitelistTableBody) {
                    // Ако таблицата вече има редове (заредени от Twig), не добавяме отново
                    if (whitelistTableBody.children.length > 0) {
                        self.updateIPTableVisibility();
                    } else {
                        // Fallback: зареждане от скритото поле (за backward compatibility)
                        const allowedIpsText = document.getElementById('allowed_ips_text');
                        if (allowedIpsText && allowedIpsText.value) {
                            const ipsText = allowedIpsText.value;
                            // Поддръжка на различни разделители (запетая или нов ред)
                            const ips = ipsText.split(/[,\n]/).filter(ip => ip.trim());
                            ips.forEach(ip => {
                                const trimmedIp = ip.trim();
                                if (trimmedIp) {
                                    self.addIPToTable(trimmedIp, 'Съществуващ IP', true);
                                }
                            });
                        }
                        self.updateIPTableVisibility();
                    }
                }

                // Инициализация на blacklist таблицата
                const blacklistTableBody = document.getElementById('blacklist-ip-addresses-table');
                if (blacklistTableBody) {
                    // Ако таблицата вече има редове (заредени от Twig), само актуализираме видимостта
                    if (blacklistTableBody.children.length > 0) {
                        self.updateBlacklistTableVisibility();
                    } else {
                        // Fallback: зареждане от скритото поле
                        const ipBlacklistField = document.getElementById('ip_blacklist');
                        if (ipBlacklistField && ipBlacklistField.value) {
                            const ipsText = ipBlacklistField.value;
                            const ips = ipsText.split(/[,\n]/).filter(ip => ip.trim());
                            ips.forEach(ip => {
                                const trimmedIp = ip.trim();
                                if (trimmedIp) {
                                    self.addIPToBlacklistTable(trimmedIp, 'Блокиран IP');
                                }
                            });
                        }
                        self.updateBlacklistTableVisibility();
                    }
                }

                self.updateCurrentIPStatus();
            },

            /**
             * Показване на modal за добавяне на IP адрес
             */
            showAddIPModal: function() {
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg max-w-md w-full mx-4">
                        <div class="flex justify-between items-center p-4 border-b">
                            <h3 class="text-lg font-semibold">Добавяне на IP адрес</h3>
                            <button class="close-modal text-gray-500 hover:text-gray-700">
                                <i class="ri-close-line text-xl"></i>
                            </button>
                        </div>
                        <form id="add-ip-form" class="p-4 space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    IP Адрес <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="ip_address" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded"
                                       placeholder="*********** или ***********/24">
                                <p class="text-xs text-gray-500 mt-1">
                                    Поддържа отделни IP адреси и CIDR нотация
                                </p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Описание
                                </label>
                                <input type="text" name="description"
                                       class="w-full px-3 py-2 border border-gray-300 rounded"
                                       placeholder="Описание на IP адреса">
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox" name="enabled" id="ip-enabled" class="mr-2" checked>
                                <label for="ip-enabled" class="text-sm text-gray-700">Активен</label>
                            </div>
                        </form>
                        <div class="flex justify-end p-4 border-t space-x-2">
                            <button class="close-modal px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                                Отказ
                            </button>
                            <button id="save-ip-address" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90">
                                Добави
                            </button>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);

                // Фокус на първото поле
                const firstInput = modal.querySelector('input[name="ip_address"]');
                if (firstInput) {
                    firstInput.focus();
                }

                // Обработка на събития в модала
                const self = this;

                // Затваряне на модала
                modal.addEventListener('click', function(e) {
                    if (e.target.classList.contains('close-modal') || e.target === modal) {
                        modal.remove();
                    }
                });

                // Запазване на IP адреса
                const saveButton = modal.querySelector('#save-ip-address');
                if (saveButton) {
                    saveButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        const form = modal.querySelector('#add-ip-form');
                        if (form) {
                            self.submitAddIPForm(form);
                        }
                    });
                }

                // Submit на формата при Enter
                const form = modal.querySelector('#add-ip-form');
                if (form) {
                    form.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.submitAddIPForm(form);
                    });
                }
            },

            /**
             * Добавяне на IP адрес в таблицата
             */
            addIPToTable: function(ip, description = '', enabled = true, ipId = null) {
                const tableBody = document.getElementById('whitelist-ip-addresses-table');
                if (!tableBody) {
                    return;
                }

                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50';
                row.dataset.ip = ip;
                if (ipId) {
                    row.dataset.ipId = ipId;
                }

                const statusClass = enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                const statusText = enabled ? 'Активен' : 'Неактивен';
                const displayDescription = description && description !== 'Съществуващ IP' ? description : 'Без описание';

                row.innerHTML = `
                    <td class="px-4 py-3 text-sm font-mono">${ip}</td>
                    <td class="px-4 py-3 text-sm text-gray-600">${displayDescription}</td>
                    <td class="px-4 py-3">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusClass}">
                            ${statusText}
                        </span>
                    </td>
                    <td class="px-4 py-3 text-right">
                        <div class="flex justify-end space-x-1">
                            <button class="toggle-ip-status p-1 text-gray-400 hover:text-primary"
                                    data-ip="${ip}" title="Промени статус">
                                <i class="ri-toggle-line"></i>
                            </button>
                            <button class="edit-ip p-1 text-gray-400 hover:text-primary"
                                    data-ip="${ip}" title="Редактирай">
                                <i class="ri-edit-line"></i>
                            </button>
                            <button class="delete-ip p-1 text-gray-400 hover:text-red-500"
                                    data-ip="${ip}" title="Изтрий">
                                <i class="ri-delete-bin-line"></i>
                            </button>
                        </div>
                    </td>
                `;

                tableBody.appendChild(row);
                this.updateIPTableVisibility();
                this.updateWhitelistCounter();
                this.updateAllowedIPsField();
                this.updateCurrentIPStatus();
            },

            /**
             * Актуализиране на видимостта на таблицата
             */
            updateIPTableVisibility: function() {
                const tableBody = document.getElementById('whitelist-ip-addresses-table');
                const noIPsMessage = document.getElementById('whitelist-no-ip-addresses');

                if (!tableBody || !noIPsMessage) {
                    return;
                }

                const hasIPs = tableBody.children.length > 0;

                if (hasIPs) {
                    noIPsMessage.classList.add('hidden');
                } else {
                    noIPsMessage.classList.remove('hidden');
                }
            },

            /**
             * Обновяване на брояча за whitelist IP адреси
             */
            updateWhitelistCounter: function() {
                const tableBody = document.getElementById('whitelist-ip-addresses-table');
                const simpleCounter = document.getElementById('whitelist-total-simple');

                if (tableBody && simpleCounter) {
                    const count = tableBody.children.length;
                    simpleCounter.textContent = count;
                }
            },

            /**
             * Обновяване на брояча за blacklist IP адреси
             */
            updateBlacklistCounter: function() {
                const tableBody = document.getElementById('blacklist-ip-addresses-table');
                const simpleCounter = document.getElementById('blacklist-total-simple');

                console.log(tableBody);
                console.log(simpleCounter);

                if (tableBody && simpleCounter) {
                    const count = tableBody.children.length;
                    simpleCounter.textContent = count;
                }
            },

            /**
             * Актуализиране на скритите полета с IP адресите
             */
            updateAllowedIPsField: function() {
                const whitelistTableBody = document.getElementById('whitelist-ip-addresses-table');
                const blacklistTableBody = document.getElementById('blacklist-ip-addresses-table');
                const ipWhitelistField = document.getElementById('ip_whitelist');
                const ipBlacklistField = document.getElementById('ip_blacklist');

                const activeIps = [];
                const blacklistIps = [];
                const whitelistData = [];
                const blacklistData = [];

                // Обработка на whitelist таблицата
                if (whitelistTableBody && whitelistTableBody.children.length > 0) {
                    Array.from(whitelistTableBody.children).forEach(row => {
                        const statusSpan = row.querySelector('span');
                        const isEnabled = statusSpan && statusSpan.textContent.trim() === 'Активен';
                        const ip = row.dataset.ip;
                        const ipId = row.dataset.ipId || null;
                        const descriptionCell = row.querySelector('td:nth-child(2)');
                        const description = descriptionCell ? descriptionCell.textContent.trim() : '';

                        // Проверка дали IP адресът е валиден и не е празен
                        if (ip && ip.trim() !== '' && ip !== '[]' && this.isValidIP(ip)) {
                            const ipData = {
                                ip: ip,
                                description: description === 'Без описание' ? '' : description,
                                status: isEnabled ? 1 : 0,
                                id: ipId
                            };
                            whitelistData.push(ipData);

                            if (isEnabled) {
                                activeIps.push(ip);
                            }
                        }
                    });
                }

                // Обработка на blacklist таблицата
                if (blacklistTableBody && blacklistTableBody.children.length > 0) {
                    Array.from(blacklistTableBody.children).forEach(row => {
                        const ip = row.dataset.ip;
                        const ipId = row.dataset.ipId || null;
                        const descriptionCell = row.querySelector('td:nth-child(2)');
                        const description = descriptionCell ? descriptionCell.textContent.trim() : '';
                        const statusSpan = row.querySelector('span');
                        const isEnabled = statusSpan && statusSpan.textContent.trim() === 'Блокиран';

                        // Проверка дали IP адресът е валиден и не е празен
                        if (ip && ip.trim() !== '' && ip !== '[]' && this.isValidIP(ip)) {
                            const ipData = {
                                ip: ip,
                                description: description === 'Без описание' ? '' : description,
                                status: isEnabled ? 1 : 0,
                                id: ipId
                            };
                            blacklistData.push(ipData);

                            if (isEnabled) {
                                blacklistIps.push(ip);
                            }
                        }
                    });
                }

                // Актуализиране на полетата с JSON структура
                if (ipWhitelistField) {
                    ipWhitelistField.value = JSON.stringify(whitelistData);
                }

                if (ipBlacklistField) {
                    ipBlacklistField.value = JSON.stringify(blacklistData);
                }

        
                // Управление на съобщенията за празни таблици
                this.toggleEmptyTableMessages();

                // Съхранение на пълните данни за AJAX операции
                this.whitelistData = whitelistData;
                this.blacklistData = blacklistData;
            },

            /**
             * Актуализиране на статуса на текущия IP
             */
            updateCurrentIPStatus: function() {
                const statusElement = document.getElementById('current-ip-status');

                if (!statusElement) {
                    return;
                }

                const currentIP = this.settings?.config?.currentIP || window.settingsConfig?.currentIP || '';

                if (!currentIP || currentIP === 'unknown') {
                    statusElement.innerHTML = '<span class="text-gray-500 font-medium">⚠ Неизвестен IP</span>';
                    return;
                }

                // Получаване на активните IP адреси от таблицата (не от скритото поле)
                const allowedIPs = this.getActiveIPsFromTable();

                // Проверка дали IP ограниченията са активирани
                const ipRestrictionEnabled = document.getElementById('ip_restriction_enabled')?.checked;

                if (!ipRestrictionEnabled) {
                    statusElement.innerHTML = '<span class="text-blue-500 font-medium">ℹ IP ограниченията са изключени</span>';
                    return;
                }

                // Проверка дали текущият IP е в списъка или съвпада с CIDR
                const isAllowed = this.checkIPInList(currentIP, allowedIPs);

                if (isAllowed) {
                    statusElement.innerHTML = '<span class="text-green-500 font-medium">✓ В списъка</span>';
                } else if (allowedIPs.length > 0) {
                    statusElement.innerHTML = '<span class="text-red-500 font-medium">⚠ Не е в списъка!</span>';
                } else {
                    statusElement.innerHTML = '<span class="text-orange-500 font-medium">⚠ Няма разрешени IP адреси</span>';
                }
            },

            /**
             * Получава активните IP адреси от таблицата
             */
            getActiveIPsFromTable: function() {
                const tableBody = document.getElementById('whitelist-ip-addresses-table');
                const activeIPs = [];

                if (tableBody) {
                    Array.from(tableBody.children).forEach(row => {
                        const statusSpan = row.querySelector('span');
                        const isEnabled = statusSpan && statusSpan.textContent.trim() === 'Активен';
                        const ip = row.dataset.ip;

                        if (ip && isEnabled) {
                            activeIPs.push(ip);
                        }
                    });
                }

                return activeIPs;
            },

            /**
             * Проверява дали IP адрес е в списъка (поддържа CIDR нотация)
             */
            checkIPInList: function(ip, ipList) {
                if (!ip || !ipList || ipList.length === 0) {
                    return false;
                }

                for (let i = 0; i < ipList.length; i++) {
                    const listItem = ipList[i].trim();

                    // Директно съвпадение
                    if (listItem === ip) {
                        return true;
                    }

                    // CIDR проверка
                    if (listItem.includes('/')) {
                        if (this.isIPInCIDR(ip, listItem)) {
                            return true;
                        }
                    }
                }

                return false;
            },

            /**
             * Проверява дали IP адрес е в CIDR диапазон
             */
            isIPInCIDR: function(ip, cidr) {
                try {
                    const [network, prefixStr] = cidr.split('/');
                    const prefix = parseInt(prefixStr, 10);

                    // Валидация
                    if (!this.isValidIP(network) || !this.isValidIP(ip) || prefix < 0 || prefix > 32) {
                        return false;
                    }

                    // Конвертиране на IP адресите в числа
                    const ipNum = this.ipToNumber(ip);
                    const networkNum = this.ipToNumber(network);

                    // Създаване на мрежова маска
                    const mask = (0xFFFFFFFF << (32 - prefix)) >>> 0;

                    // Проверка дали IP адресът е в мрежата
                    return (ipNum & mask) === (networkNum & mask);
                } catch (e) {
                    return false;
                }
            },

            /**
             * Конвертира IP адрес в число
             */
            ipToNumber: function(ip) {
                const parts = ip.split('.');
                return (parseInt(parts[0], 10) << 24) +
                       (parseInt(parts[1], 10) << 16) +
                       (parseInt(parts[2], 10) << 8) +
                       parseInt(parts[3], 10);
            },

            /**
             * Изтриване на IP адрес
             */
            deleteIPAddress: function(ip) {
                const self = this;

                if (!confirm('Сигурни ли сте, че искате да изтриете този IP адрес?')) {
                    return;
                }

                const row = document.querySelector(`#whitelist-ip-addresses-table tr[data-ip="${ip}"]`);
                if (!row) {
                    self.showSettingsNotification('IP адресът не е намерен', 'error');
                    return;
                }

                const ipId = row.dataset.ipId;
                self.ajaxDeleteIPAddress(ip, ipId, row, 'whitelist');
            },

            /**
             * AJAX изтриване на IP адрес
             */
            ajaxDeleteIPAddress: function(ipAddress, ipId, row, listType) {
                const self = this;

                const formData = new FormData();
                formData.append('action', 'delete_ip');
                formData.append('ip_id', ipId || '');
                formData.append('ip_address', ipAddress);
                formData.append('list_type', listType);
                formData.append('user_token', self.settings?.config?.userToken || window.settingsConfig?.userToken || '');

                // Показване на loading индикатор на реда
                row.style.opacity = '0.5';
                row.style.pointerEvents = 'none';

                const ajaxUrl = self.settings?.config?.ajaxUrls?.security_update || window.settingsConfig?.ajaxUrls?.security_update || (window.location.href.replace(/&route=[^&]*/, '') + '&route=setting/setting/securityUpdate');

                fetch(ajaxUrl, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Премахване на реда от таблицата
                        row.remove();

                        console.log(listType);

                        // Актуализиране на видимостта и полетата
                        if (listType === 'whitelist') {
                            self.updateIPTableVisibility();
                            self.updateWhitelistCounter();
                        } else {
                            self.updateBlacklistTableVisibility();
                            self.updateBlacklistCounter();
                        }

                        self.updateAllowedIPsField();
                        self.updateCurrentIPStatus();

                        self.showSettingsNotification('IP адресът е изтрит успешно', 'success');
                    } else {
                        // Възстановяване на реда при грешка
                        row.style.opacity = '1';
                        row.style.pointerEvents = 'auto';

                        self.showSettingsNotification(data.error || 'Грешка при изтриване на IP адрес', 'error');
                    }
                })
                .catch(error => {
                    console.error('AJAX error:', error);

                    // Възстановяване на реда при грешка
                    row.style.opacity = '1';
                    row.style.pointerEvents = 'auto';

                    self.showSettingsNotification('Възникна грешка при изтриване на IP адрес', 'error');
                });
            },

            /**
             * Промяна на статуса на IP адрес
             */
            toggleIPStatus: function(ip) {
                const row = document.querySelector(`tr[data-ip="${ip}"]`);
                if (!row) {
                    return;
                }

                const statusSpan = row.querySelector('span');
                const isCurrentlyEnabled = statusSpan.textContent.trim() === 'Активен';

                if (isCurrentlyEnabled) {
                    statusSpan.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800';
                    statusSpan.textContent = 'Неактивен';
                } else {
                    statusSpan.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800';
                    statusSpan.textContent = 'Активен';
                }

                this.updateAllowedIPsField();
                this.updateCurrentIPStatus();
                this.showSettingsNotification('Статусът на IP адреса е променен', 'success');
            },

            /**
             * Редактиране на IP адрес
             */
            editIPAddress: function(ip) {
                const self = this;
                const row = document.querySelector(`#whitelist-ip-addresses-table tr[data-ip="${ip}"]`);

                if (!row) {
                    self.showSettingsNotification('IP адресът не е намерен в таблицата', 'error');
                    return;
                }

                // Получаване на текущите данни
                const currentDescription = row.querySelector('td:nth-child(2)').textContent.trim();
                const statusSpan = row.querySelector('span');
                const currentStatus = statusSpan && statusSpan.textContent.trim() === 'Активен';

                // Създаване на modal за редактиране
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg max-w-md w-full mx-4">
                        <div class="flex justify-between items-center p-4 border-b">
                            <h3 class="text-lg font-semibold">Редактиране на IP адрес</h3>
                            <button class="close-modal text-gray-500 hover:text-gray-700">
                                <i class="ri-close-line text-xl"></i>
                            </button>
                        </div>
                        <form id="edit-ip-form" class="p-4 space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    IP Адрес <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="ip_address" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded"
                                       value="${ip}" readonly>
                                <p class="text-xs text-gray-500 mt-1">
                                    IP адресът не може да се променя. За промяна изтрийте и добавете нов.
                                </p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Описание
                                </label>
                                <input type="text" name="description"
                                       class="w-full px-3 py-2 border border-gray-300 rounded"
                                       value="${currentDescription === 'Съществуващ IP' || currentDescription === '-' ? '' : currentDescription}"
                                       placeholder="Описание на IP адреса">
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox" name="enabled" id="edit-ip-enabled" class="mr-2" ${currentStatus ? 'checked' : ''}>
                                <label for="edit-ip-enabled" class="text-sm text-gray-700">Активен</label>
                            </div>
                        </form>
                        <div class="flex justify-end p-4 border-t space-x-2">
                            <button class="close-modal px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                                Отказ
                            </button>
                            <button id="save-ip-changes" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90">
                                Запази промените
                            </button>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);

                // Фокус на полето за описание
                const descriptionInput = modal.querySelector('input[name="description"]');
                if (descriptionInput) {
                    descriptionInput.focus();
                    descriptionInput.select();
                }

                // Обработка на събития в модала
                // Затваряне на модала
                modal.addEventListener('click', function(e) {
                    if (e.target.classList.contains('close-modal') || e.target === modal) {
                        modal.remove();
                    }
                });

                // Запазване на промените
                const saveButton = modal.querySelector('#save-ip-changes');
                if (saveButton) {
                    saveButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        const form = modal.querySelector('#edit-ip-form');
                        if (form) {
                            self.submitEditIPForm(form, row);
                        }
                    });
                }

                // Submit на формата при Enter
                const form = modal.querySelector('#edit-ip-form');
                if (form) {
                    form.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.submitEditIPForm(form, row);
                    });
                }
            },

            /**
             * Обработка на формата за редактиране на IP адрес
             */
            submitEditIPForm: function(form, row) {
                const self = this;
                const formData = new FormData(form);

                const description = formData.get('description').trim();
                const enabled = formData.get('enabled') === 'on';

                // Актуализиране на реда в таблицата
                const descriptionCell = row.querySelector('td:nth-child(2)');
                const statusCell = row.querySelector('td:nth-child(3) span');

                if (descriptionCell) {
                    descriptionCell.textContent = description || 'Без описание';
                }

                if (statusCell) {
                    if (enabled) {
                        statusCell.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800';
                        statusCell.textContent = 'Активен';
                    } else {
                        statusCell.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800';
                        statusCell.textContent = 'Неактивен';
                    }
                }

                // AJAX актуализиране на IP адреса
                const ipId = row.dataset.ipId;
                const originalIP = row.dataset.ip; // Извличане на IP адреса от реда
                self.ajaxUpdateIPAddress(originalIP, description, enabled, ipId, form, row);
            },

            /**
             * AJAX актуализиране на IP адрес
             */
            ajaxUpdateIPAddress: function(ipAddress, description, enabled, ipId, form, row) {
                const self = this;

                // Показване на loading индикатор
                const submitButton = form.querySelector('#save-ip-changes');
                const originalText = submitButton ? submitButton.textContent : '';
                if (submitButton) {
                    submitButton.disabled = true;
                    submitButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }

                const formData = new FormData();
                formData.append('action', 'update_ip');
                formData.append('ip_id', ipId || '');
                formData.append('ip_address', ipAddress);
                formData.append('description', description);
                formData.append('status', enabled ? '1' : '0');
                formData.append('list_type', 'whitelist');
                formData.append('user_token', self.settings?.config?.userToken || window.settingsConfig?.userToken || '');

                const ajaxUrl = self.settings?.config?.ajaxUrls?.security_update || window.settingsConfig?.ajaxUrls?.security_update || (window.location.href.replace(/&route=[^&]*/, '') + '&route=setting/setting/securityUpdate');

                fetch(ajaxUrl, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Актуализиране на реда в таблицата
                        const descriptionCell = row.querySelector('td:nth-child(2)');
                        const statusCell = row.querySelector('td:nth-child(3) span');

                        if (descriptionCell) {
                            descriptionCell.textContent = description || 'Без описание';
                        }

                        if (statusCell) {
                            if (enabled) {
                                statusCell.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800';
                                statusCell.textContent = 'Активен';
                            } else {
                                statusCell.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800';
                                statusCell.textContent = 'Неактивен';
                            }
                        }

                        // Актуализиране на скритите полета
                        self.updateAllowedIPsField();
                        self.updateCurrentIPStatus();

                        // Затваряне на модала
                        const modal = form.closest('.fixed.inset-0');
                        if (modal) {
                            modal.remove();
                        }

                        self.showSettingsNotification('IP адресът е актуализиран успешно', 'success');
                    } else {
                        self.showSettingsNotification(data.error || 'Грешка при актуализиране на IP адрес', 'error');

                        // Възстановяване на бутона
                        if (submitButton) {
                            submitButton.disabled = false;
                            submitButton.textContent = originalText;
                        }
                    }
                })
                .catch(error => {
                    console.error('AJAX error:', error);
                    self.showSettingsNotification('Възникна грешка при актуализиране на IP адрес', 'error');

                    // Възстановяване на бутона
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.textContent = originalText;
                    }
                });
            },

            /**
             * Добавя текущия IP адрес към whitelist
             */
            addCurrentIPToWhitelist: function() {
                const currentIP = this.settings?.config?.currentIP || window.settingsConfig?.currentIP || '';
               

                console.log('Current IP:', currentIP);
                console.log(window.settingsConfig);

                if (!currentIP || currentIP === 'unknown') {
                    this.showSettingsNotification('Не може да се определи текущият IP адрес. Моля, презаредете страницата.', 'error');
                    console.error('Current IP not found in config:', {
                        settingsConfig: this.settings?.config,
                        windowConfig: window.settingsConfig
                    });
                    return;
                }

                // Валидация на IP адреса
                if (!this.isValidIP(currentIP)) {
                    this.showSettingsNotification('Текущият IP адрес не е валиден: ' + currentIP, 'error');
                    return;
                }

                // Проверка дали IP адресът вече съществува в таблицата
                const existingRow = document.querySelector(`#whitelist-ip-addresses-table tr[data-ip="${currentIP}"]`);
                if (existingRow) {
                    this.showSettingsNotification('Текущият IP адрес (' + currentIP + ') вече е в списъка', 'warning');
                    return;
                }

                // Проверка дали IP адресът е покрит от съществуващ CIDR диапазон
                const activeIPs = this.getActiveIPsFromTable();
                if (this.checkIPInList(currentIP, activeIPs)) {
                    this.showSettingsNotification('Текущият IP адрес (' + currentIP + ') е покрит от съществуващ диапазон', 'info');
                    return;
                }

                // Добавяне на IP адреса в таблицата
                this.addIPToTable(currentIP, 'Текущ IP адрес', true);
                this.showSettingsNotification('Текущият IP адрес (' + currentIP + ') е добавен успешно', 'success');

                // Актуализиране на статуса
                this.updateCurrentIPStatus();
            },

            /**
             * Показване на modal за добавяне на blacklist IP адрес
             */
            showAddBlacklistIPModal: function() {
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg max-w-md w-full mx-4">
                        <div class="flex justify-between items-center p-4 border-b">
                            <h3 class="text-lg font-semibold">Добавяне на IP адрес в Blacklist</h3>
                            <button class="close-modal text-gray-500 hover:text-gray-700">
                                <i class="ri-close-line text-xl"></i>
                            </button>
                        </div>
                        <form id="add-blacklist-ip-form" class="p-4 space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    IP Адрес <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="ip_address" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded"
                                       placeholder="*********** или ***********/24">
                                <p class="text-xs text-gray-500 mt-1">
                                    Поддържа отделни IP адреси и CIDR нотация
                                </p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Описание
                                </label>
                                <input type="text" name="description"
                                       class="w-full px-3 py-2 border border-gray-300 rounded"
                                       placeholder="Причина за блокиране">
                            </div>
                        </form>
                        <div class="flex justify-end p-4 border-t space-x-2">
                            <button class="close-modal px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                                Отказ
                            </button>
                            <button id="save-blacklist-ip-address" class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
                                Блокирай
                            </button>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);

                // Фокус на първото поле
                const firstInput = modal.querySelector('input[name="ip_address"]');
                if (firstInput) {
                    firstInput.focus();
                }

                // Обработка на събития в модала
                const self = this;

                // Затваряне на модала
                modal.addEventListener('click', function(e) {
                    if (e.target.classList.contains('close-modal') || e.target === modal) {
                        modal.remove();
                    }
                });

                // Запазване на IP адреса
                const saveButton = modal.querySelector('#save-blacklist-ip-address');
                if (saveButton) {
                    saveButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        const form = modal.querySelector('#add-blacklist-ip-form');
                        if (form) {
                            self.submitAddBlacklistIPForm(form);
                        }
                    });
                }

                // Submit на формата при Enter
                const form = modal.querySelector('#add-blacklist-ip-form');
                if (form) {
                    form.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.submitAddBlacklistIPForm(form);
                    });
                }
            },

            /**
             * Обработка на формата за добавяне на blacklist IP адрес
             */
            submitAddBlacklistIPForm: function(form) {
                const self = this;
                const formData = new FormData(form);

                const ipAddress = formData.get('ip_address').trim();
                const description = formData.get('description').trim();

                // Валидация на IP адреса
                if (!ipAddress) {
                    self.showSettingsNotification('Моля, въведете IP адрес', 'error');
                    return;
                }

                // Валидация на формата на IP адреса
                if (!self.isValidIP(ipAddress) && !self.isValidCIDR(ipAddress)) {
                    self.showSettingsNotification('Моля, въведете валиден IP адрес или CIDR нотация', 'error');
                    return;
                }

                // Проверка дали IP адресът вече съществува в blacklist таблицата
                const existingRow = document.querySelector(`#blacklist-addresses-table tr[data-ip="${ipAddress}"]`);
                if (existingRow) {
                    self.showSettingsNotification('Този IP адрес вече е в blacklist', 'error');
                    return;
                }

                // Проверка в скритото поле за blacklist
                const ipBlacklistField = document.getElementById('ip_blacklist');
                if (ipBlacklistField && ipBlacklistField.value) {
                    const existingBlacklist = ipBlacklistField.value.split(',').map(ip => ip.trim());
                    if (existingBlacklist.includes(ipAddress)) {
                        self.showSettingsNotification('Този IP адрес вече е в blacklist', 'error');
                        return;
                    }
                }

                // AJAX добавяне на IP адреса в blacklist
                self.ajaxAddBlacklistIPAddress(ipAddress, description, form);
            },

            /**
             * AJAX добавяне на blacklist IP адрес
             */
            ajaxAddBlacklistIPAddress: function(ipAddress, description, form) {
                const self = this;

                // Показване на loading индикатор
                const submitButton = form.querySelector('button[type="submit"], #save-blacklist-ip-address');
                const originalText = submitButton ? submitButton.textContent : '';
                if (submitButton) {
                    submitButton.disabled = true;
                    submitButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Добавяне...';
                }

                const formData = new FormData();
                formData.append('action', 'add_ip');
                formData.append('ip_address', ipAddress);
                formData.append('description', description);
                formData.append('status', '1');
                formData.append('list_type', 'blacklist');
                formData.append('user_token', self.settings?.config?.userToken || window.settingsConfig?.userToken || '');

                const ajaxUrl = self.settings?.config?.ajaxUrls?.security_update || window.settingsConfig?.ajaxUrls?.security_update || (window.location.href.replace(/&route=[^&]*/, '') + '&route=setting/setting/securityUpdate');

                fetch(ajaxUrl, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Добавяне на IP адреса в blacklist таблицата с ID от сървъра
                        self.addIPToBlacklistTable(ipAddress, description || 'Блокиран IP', data.ip_id);

                        // Затваряне на модала
                        const modal = form.closest('.fixed.inset-0');
                        if (modal) {
                            modal.remove();
                        }

                        self.showSettingsNotification('IP адресът е добавен в blacklist успешно', 'success');
                    } else {
                        self.showSettingsNotification(data.error || 'Грешка при добавяне на IP адрес в blacklist', 'error');

                        // Възстановяване на бутона
                        if (submitButton) {
                            submitButton.disabled = false;
                            submitButton.textContent = originalText;
                        }
                    }
                })
                .catch(error => {
                    console.error('AJAX error:', error);
                    self.showSettingsNotification('Възникна грешка при добавяне на IP адрес в blacklist', 'error');

                    // Възстановяване на бутона
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.textContent = originalText;
                    }
                });
            },

            /**
             * Добавяне на IP адрес в blacklist таблицата
             */
            addIPToBlacklistTable: function(ip, description = '', ipId = null) {
                const tableBody = document.getElementById('blacklist-ip-addresses-table');
                if (!tableBody) {
                    return;
                }

                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50';
                row.dataset.ip = ip;
                if (ipId) {
                    row.dataset.ipId = ipId;
                }

                row.innerHTML = `
                    <td class="px-4 py-3 text-sm font-mono">${ip}</td>
                    <td class="px-4 py-3 text-sm text-gray-600">${description || '-'}</td>
                    <td class="px-4 py-3">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                            Блокиран
                        </span>
                    </td>
                    <td class="px-4 py-3 text-right">
                        <div class="flex justify-end space-x-1">
                            <button class="delete-blacklist-ip p-1 text-gray-400 hover:text-red-500"
                                    data-ip="${ip}" title="Изтрий">
                                <i class="ri-delete-bin-line"></i>
                            </button>
                        </div>
                    </td>
                `;

                tableBody.appendChild(row);
                this.updateBlacklistTableVisibility();
                this.updateBlacklistCounter();
                this.updateAllowedIPsField();
            },

            /**
             * Актуализиране на видимостта на blacklist таблицата
             */
            updateBlacklistTableVisibility: function() {
                const tableBody = document.getElementById('blacklist-ip-addresses-table');
                const noIPsMessage = document.getElementById('blacklist-no-ip-addresses');

                if (!tableBody || !noIPsMessage) {
                    return;
                }

                const hasIPs = tableBody.children.length > 0;

                if (hasIPs) {
                    noIPsMessage.classList.add('hidden');
                } else {
                    noIPsMessage.classList.remove('hidden');
                }
            },

            /**
             * Изтриване на blacklist IP адрес
             */
            deleteBlacklistIPAddress: function(ip) {
                const self = this;

                if (!confirm('Сигурни ли сте, че искате да премахнете този IP адрес от blacklist?')) {
                    return;
                }

                const row = document.querySelector(`#blacklist-ip-addresses-table tr[data-ip="${ip}"]`);
                if (!row) {
                    self.showSettingsNotification('IP адресът не е намерен в blacklist', 'error');
                    return;
                }

                const ipId = row.dataset.ipId;
                self.ajaxDeleteIPAddress(ip, ipId, row, 'blacklist');
            },

            /**
             * Обработка на формата за добавяне на IP адрес
             */
            submitAddIPForm: function(form) {
                const self = this;
                const formData = new FormData(form);

                const ipAddress = formData.get('ip_address').trim();
                const description = formData.get('description').trim();
                const enabled = formData.get('enabled') === 'on';

                // Валидация на IP адреса
                if (!ipAddress) {
                    self.showSettingsNotification('Моля, въведете IP адрес', 'error');
                    return;
                }

                // Валидация на формата на IP адреса
                if (!self.isValidIP(ipAddress) && !self.isValidCIDR(ipAddress)) {
                    self.showSettingsNotification('Моля, въведете валиден IP адрес или CIDR нотация', 'error');
                    return;
                }

                // Проверка дали IP адресът вече съществува в таблицата
                const existingRow = document.querySelector(`tr[data-ip="${ipAddress}"]`);
                if (existingRow) {
                    self.showSettingsNotification('Този IP адрес вече съществува в списъка', 'error');
                    return;
                }

                // Допълнителна проверка в скритите полета
                const ipWhitelistField = document.getElementById('ip_whitelist');
                const ipBlacklistField = document.getElementById('ip_blacklist');

                if (ipWhitelistField && ipWhitelistField.value) {
                    const existingWhitelist = ipWhitelistField.value.split(',').map(ip => ip.trim());
                    if (existingWhitelist.includes(ipAddress)) {
                        self.showSettingsNotification('Този IP адрес вече съществува в whitelist', 'error');
                        return;
                    }
                }

                if (ipBlacklistField && ipBlacklistField.value) {
                    const existingBlacklist = ipBlacklistField.value.split(',').map(ip => ip.trim());
                    if (existingBlacklist.includes(ipAddress)) {
                        self.showSettingsNotification('Този IP адрес вече съществува в blacklist', 'error');
                        return;
                    }
                }

                // AJAX добавяне на IP адреса
                self.ajaxAddIPAddress(ipAddress, description, enabled, form);
            },

            /**
             * AJAX добавяне на IP адрес
             */
            ajaxAddIPAddress: function(ipAddress, description, enabled, form) {
                const self = this;

                // Показване на loading индикатор
                const submitButton = form.querySelector('button[type="submit"], #save-ip-address');
                const originalText = submitButton ? submitButton.textContent : '';
                if (submitButton) {
                    submitButton.disabled = true;
                    submitButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Добавяне...';
                }

                const formData = new FormData();
                formData.append('action', 'add_ip');
                formData.append('ip_address', ipAddress);
                formData.append('description', description);
                formData.append('status', enabled ? '1' : '0');
                formData.append('list_type', 'whitelist');
                formData.append('user_token', self.settings?.config?.userToken || window.settingsConfig?.userToken || '');

                const ajaxUrl = self.settings?.config?.ajaxUrls?.security_update || window.settingsConfig?.ajaxUrls?.security_update || (window.location.href.replace(/&route=[^&]*/, '') + '&route=setting/setting/securityUpdate');

                fetch(ajaxUrl, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Добавяне на IP адреса в таблицата с ID от сървъра
                        self.addIPToTable(ipAddress, description, enabled, data.ip_id);

                        // Затваряне на модала
                        const modal = form.closest('.fixed.inset-0');
                        if (modal) {
                            modal.remove();
                        }

                        self.showSettingsNotification('IP адресът е добавен успешно', 'success');
                    } else {
                        self.showSettingsNotification(data.error || 'Грешка при добавяне на IP адрес', 'error');

                        // Възстановяване на бутона
                        if (submitButton) {
                            submitButton.disabled = false;
                            submitButton.textContent = originalText;
                        }
                    }
                })
                .catch(error => {
                    console.error('AJAX error:', error);
                    self.showSettingsNotification('Възникна грешка при добавяне на IP адрес', 'error');

                    // Възстановяване на бутона
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.textContent = originalText;
                    }
                });
            },

            /**
             * Свързване на toggle за IP whitelist
             */
            bindIPWhitelistToggle: function() {
                // Тази функционалност вече се обработва от toggleIPRestrictionFields
                // Запазваме метода за съвместимост
            }
        });
    }



    /**
     * Клас за управление на pagination на IP таблиците
     */
    function IPTablePagination(tableType) {
        this.tableType = tableType; // 'whitelist' или 'blacklist'
        this.currentPage = 1;
        this.perPage = 20;
        this.totalRecords = 0;
        this.totalPages = 1;

        const pagination = document.getElementById(tableType + '-pagination');

        if (!pagination) {
            console.error('Pagination element ' + tableType + '-pagination not found');
            return;
        }

        this.init();
    }

    IPTablePagination.prototype = {
        init: function() {
            this.bindEvents();
            this.loadPage(1);
            // Първоначално показване/скриване на pagination
            this.togglePaginationVisibility();
        },

        bindEvents: function() {
            const self = this;
            const prefix = this.tableType;

            // Бутони за навигация
            document.getElementById(`${prefix}-first-page`)?.addEventListener('click', () => self.goToPage(1));
            document.getElementById(`${prefix}-prev-page`)?.addEventListener('click', () => self.goToPage(self.currentPage - 1));
            document.getElementById(`${prefix}-next-page`)?.addEventListener('click', () => self.goToPage(self.currentPage + 1));
            document.getElementById(`${prefix}-last-page`)?.addEventListener('click', () => self.goToPage(self.totalPages));

            // Промяна на броя записи на страница
            document.getElementById(`${prefix}-per-page`)?.addEventListener('change', function() {
                self.perPage = parseInt(this.value);
                self.loadPage(1);
            });
        },

        loadPage: function(page) {
            const self = this;

            if (page < 1 || (page > this.totalPages && this.totalPages > 0)) {
                return;
            }

 
            this.currentPage = page;

            // AJAX заявка за зареждане на данните
            const formData = new FormData();
            formData.append('action', 'get_ip_page');
            formData.append('table_type', this.tableType);
            formData.append('page', this.currentPage);
            formData.append('per_page', this.perPage);
            formData.append('user_token', window.settingsConfig?.userToken || '');

            fetch(window.settingsConfig?.ajaxUrls?.security_pagination || '', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    self.updateTable(data.data);
                    self.updatePaginationInfo(data.pagination);
                } else {
                    console.error('Pagination error:', data.error);
                }
            })
            .catch(error => {
                console.error('Pagination AJAX error:', error);
            });
        },

        updateTable: function(ipData) {
            const tableBody = document.getElementById(`${this.tableType === 'whitelist' ? 'whitelist-ip-addresses-table' : 'blacklist-ip-addresses-table'}`);
            if (!tableBody) return;

            // Изчистване на таблицата
            tableBody.innerHTML = '';

            if (ipData.length === 0) {
                // Показване на съобщение за празна таблица
                const emptyMessage = document.getElementById(`${this.tableType === 'whitelist' ? 'whitelist-no-ip-addresses' : 'blacklist-no-ip-addresses'}`);
                if (emptyMessage) {
                    emptyMessage.style.display = 'block';
                }
                return;
            }

            // Скриване на съобщението за празна таблица
            const emptyMessage = document.getElementById(`${this.tableType === 'whitelist' ? 'whitelist-no-ip-addresses' : 'blacklist-no-ip-addresses'}`);
            if (emptyMessage) {
                emptyMessage.style.display = 'none';
            }

            // Добавяне на редовете
            ipData.forEach(ip => {
                const row = this.createTableRow(ip);
                tableBody.appendChild(row);
            });

            // Актуализиране на бутона за текущия IP (само за whitelist)
            if (this.tableType === 'whitelist' && typeof BackendModule !== 'undefined' && BackendModule.toggleCurrentIPButton) {
                BackendModule.toggleCurrentIPButton();
            }
        },

        createTableRow: function(ipData) {
            const row = document.createElement('tr');
            row.className = 'hover:bg-gray-50';
            row.setAttribute('data-ip', ipData.ip_address);
            row.setAttribute('data-ip-id', ipData.id);

            const statusClass = ipData.status == 1 ?
                (this.tableType === 'whitelist' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800') :
                'bg-gray-100 text-gray-600';

            const statusText = ipData.status == 1 ?
                (this.tableType === 'whitelist' ? 'Активен' : 'Блокиран') :
                'Неактивен';

            const description = ipData.description || 'Без описание';

            row.innerHTML = `
                <td class="px-4 py-3 text-sm font-mono">${ipData.ip_address}</td>
                <td class="px-4 py-3 text-sm text-gray-600">${description}</td>
                <td class="px-4 py-3">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusClass}">
                        ${statusText}
                    </span>
                </td>
                <td class="px-4 py-3 text-right">
                    <div class="flex justify-end space-x-1">
                        ${this.tableType === 'whitelist' ? `
                            <button class="toggle-ip-status p-1 text-gray-400 hover:text-primary"
                                    data-ip="${ipData.ip_address}" title="Промени статус">
                                <i class="ri-toggle-line"></i>
                            </button>
                            <button class="edit-ip p-1 text-gray-400 hover:text-primary"
                                    data-ip="${ipData.ip_address}" title="Редактирай">
                                <i class="ri-edit-line"></i>
                            </button>
                        ` : ''}
                        <button class="${this.tableType === 'whitelist' ? 'delete-ip' : 'delete-blacklist-ip'} p-1 text-gray-400 hover:text-red-500"
                                data-ip="${ipData.ip_address}" title="Изтрий">
                            <i class="ri-delete-bin-line"></i>
                        </button>
                    </div>
                </td>
            `;

            return row;
        },

        updatePaginationInfo: function(paginationData) {
            this.totalRecords = paginationData.total;
            this.totalPages = paginationData.pages;
            this.currentPage = paginationData.current_page;

            const prefix = this.tableType;

            // Актуализиране на информацията
            document.getElementById(`${prefix}-total`).textContent = this.totalRecords;
            document.getElementById(`${prefix}-current-page`).textContent = this.currentPage;
            document.getElementById(`${prefix}-total-pages`).textContent = this.totalPages;

            // Актуализиране на простия брояч
            const simpleCounter = document.getElementById(`${prefix}-total-simple`);
            if (simpleCounter) {
                simpleCounter.textContent = this.totalRecords;
            }

            // Показване/скриване на pagination спрямо броя записи
            this.togglePaginationVisibility();

            // Актуализиране на състоянието на бутоните
            this.updateNavigationButtons();
        },

        updateNavigationButtons: function() {
            const prefix = this.tableType;

            const firstBtn = document.getElementById(`${prefix}-first-page`);
            const prevBtn = document.getElementById(`${prefix}-prev-page`);
            const nextBtn = document.getElementById(`${prefix}-next-page`);
            const lastBtn = document.getElementById(`${prefix}-last-page`);

            const isFirstPage = this.currentPage <= 1;
            const isLastPage = this.currentPage >= this.totalPages;

            if (firstBtn) firstBtn.disabled = isFirstPage;
            if (prevBtn) prevBtn.disabled = isFirstPage;
            if (nextBtn) nextBtn.disabled = isLastPage;
            if (lastBtn) lastBtn.disabled = isLastPage;
        },

        goToPage: function(page) {
            if (page >= 1 && page <= this.totalPages) {
                this.loadPage(page);
            }
        },

        refresh: function() {
            this.loadPage(this.currentPage);
        },

        togglePaginationVisibility: function() {
            const prefix = this.tableType;
            const paginationElement = document.getElementById(`${prefix}-pagination`);
            const simpleCounterElement = document.getElementById(`${prefix}-simple-counter`);

            if (this.totalRecords <= 20) {
                // Скриване на pagination, показване на прост брояч
                if (paginationElement) {
                    paginationElement.classList.add('hidden');
                }
                if (simpleCounterElement) {
                    simpleCounterElement.classList.remove('hidden');
                }
            } else {
                // Показване на pagination, скриване на прост брояч
                if (paginationElement) {
                    paginationElement.classList.remove('hidden');
                }
                if (simpleCounterElement) {
                    simpleCounterElement.classList.add('hidden');
                }
            }
        }
    };

    // Създаване на глобален обект за достъп от HTML
    window.SecuritySettings = {
        addCurrentIPToWhitelist: function() {
            if (typeof BackendModule !== 'undefined' && BackendModule.addCurrentIPToWhitelist) {
                BackendModule.addCurrentIPToWhitelist();
            } else {
                console.error('BackendModule not initialized');
            }
        },
        showAddBlacklistIPModal: function() {
            if (typeof BackendModule !== 'undefined' && BackendModule.showAddBlacklistIPModal) {
                BackendModule.showAddBlacklistIPModal();
            } else {
                console.error('BackendModule not initialized');
            }
        }
    };

    // Инициализация при зареждане на DOM
    document.addEventListener('DOMContentLoaded', function() {
        initSecurityModule();
    });

})();
