<div class="mb-6">
    <h1 class="text-2xl font-bold text-gray-800">Настройки за сигурност</h1>
    <p class="text-gray-600 mt-1">Управлявайте сигурността на вашия административен панел</p>
</div>

<form id="security-settings-form" class="space-y-6">
    <div class="max-w-screen-xl">
        <div class="grid grid-cols-1 gap-6">
        <!-- Security Settings -->
        <div class="col-span-1">
            <!-- IP Restrictions -->
            <div class="bg-white rounded shadow p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-shield-line mr-2"></i>
                    IP ограничения
                </h2>
                
                <div class="space-y-4">
                    <div class="flex items-center space-x-3">
                        <input type="checkbox"
                               id="ip_restriction_enabled"
                               name="ip_restriction_enabled"
                               class="toggle-switch"
                               {{ ip_restriction_enabled ? 'checked' : '' }}>
                        <div>
                            <label for="ip_restriction_enabled" class="text-sm font-medium text-gray-700">
                                Активиране на IP ограничения
                            </label>
                            <p class="text-xs text-gray-500">Разрешава достъп само от определени IP адреси</p>
                        </div>
                    </div>

                    <div id="ip-restriction-fields" class="{{ ip_restriction_enabled ? '' : 'hidden' }}">
                        <div>
                            <div class="flex justify-between items-center mb-3">
                                <label class="block text-sm font-medium text-gray-700">
                                    Разрешени IP адреси
                                </label>
                                <button type="button" id="add-ip-address" class="px-3 py-1 bg-primary text-white text-sm rounded hover:bg-primary/90">
                                    <i class="ri-add-line mr-1"></i>Добави IP
                                </button>
                            </div>

                            <div class="border border-gray-300 rounded">
                                <table class="w-full">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP Адрес</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Описание</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Статус</th>
                                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Действия</th>
                                        </tr>
                                    </thead>
                                    <tbody id="whitelist-ip-addresses-table" class="bg-white divide-y divide-gray-200">
                                        <!-- IP адресите се зареждат от новите таблици -->
                                        {% if whitelist_ips and whitelist_ips|length > 0 %}
                                            {% for ip_data in whitelist_ips %}
                                                {% if ip_data.ip_address and ip_data.ip_address != '[]' %}
                                                    {% set status_class = ip_data.status == 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %}
                                                    {% set status_text = ip_data.status == 1 ? 'Активен' : 'Неактивен' %}
                                                    {% set description = ip_data.description ? ip_data.description : 'Без описание' %}

                                                    <tr class="hover:bg-gray-50" data-ip="{{ ip_data.ip_address }}" data-ip-id="{{ ip_data.id }}">
                                                        <td class="px-4 py-3 text-sm font-mono">{{ ip_data.ip_address }}</td>
                                                        <td class="px-4 py-3 text-sm text-gray-600">{{ description }}</td>
                                                        <td class="px-4 py-3">
                                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{' '}}{{ status_class }}">
                                                                {{ status_text }}
                                                            </span>
                                                        </td>
                                                        <td class="px-4 py-3 text-right">
                                                            <div class="flex justify-end space-x-1">
                                                                <button class="toggle-ip-status p-1 text-gray-400 hover:text-primary"
                                                                        data-ip="{{ ip_data.ip_address }}" title="Промени статус">
                                                                    <i class="ri-toggle-line"></i>
                                                                </button>
                                                                <button class="edit-ip p-1 text-gray-400 hover:text-primary"
                                                                        data-ip="{{ ip_data.ip_address }}" title="Редактирай">
                                                                    <i class="ri-edit-line"></i>
                                                                </button>
                                                                <button class="delete-ip p-1 text-gray-400 hover:text-red-500"
                                                                        data-ip="{{ ip_data.ip_address }}" title="Изтрий">
                                                                    <i class="ri-delete-bin-line"></i>
                                                                </button>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                {% endif %}
                                            {% endfor %}
                                        {% endif %}

                                    </tbody>
                                </table>

                                <!-- Whitelist Pagination -->
                                <div id="whitelist-pagination" class="{% if whitelist_total <= 20 %}{{' '}}hidden{% endif %} flex items-center justify-between px-4 py-3 bg-gray-50 border-t border-gray-200">
                                    <div class="flex items-center space-x-2">
                                        <span class="text-sm text-gray-700">Показване на</span>
                                        <select id="whitelist-per-page" class="border border-gray-300 rounded px-2 py-1 text-sm">
                                            <option value="10">10</option>
                                            <option value="20" selected>20</option>
                                            <option value="50">50</option>
                                            <option value="100">100</option>
                                        </select>
                                        <span class="text-sm text-gray-700">от <span id="whitelist-total">{{ whitelist_total ?? 0 }}</span> записа</span>
                                    </div>

                                    <div class="flex items-center space-x-1">
                                        <button id="whitelist-first-page" class="px-2 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed">
                                            <i class="ri-skip-back-line"></i>
                                        </button>
                                        <button id="whitelist-prev-page" class="px-2 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed">
                                            <i class="ri-arrow-left-s-line"></i>
                                        </button>
                                        <span class="px-3 py-1 text-sm">
                                            Страница <span id="whitelist-current-page">1</span> от <span id="whitelist-total-pages">1</span>
                                        </span>
                                        <button id="whitelist-next-page" class="px-2 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed">
                                            <i class="ri-arrow-right-s-line"></i>
                                        </button>
                                        <button id="whitelist-last-page" class="px-2 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed">
                                            <i class="ri-skip-forward-line"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Whitelist Simple Counter -->
                                <div id="whitelist-simple-counter" class="{% if whitelist_total > 20 %}{{' '}}hidden{% endif %} px-4 py-3 bg-gray-50 border-t border-gray-200 text-center">
                                    <span class="text-sm text-gray-700">Общо <span id="whitelist-total-simple">{{ whitelist_total ?? 0 }}</span> записа</span>
                                </div>

                                <div id="whitelist-no-ip-addresses" class="text-center py-8 text-gray-500 {% if whitelist_ips and whitelist_ips|length > 0 %}{{' '}}hidden{% endif %}">
                                    <i class="ri-information-line text-2xl mb-2"></i>
                                    <p>Няма добавени IP адреси</p>
                                    <p class="text-xs">Кликнете "Добави IP" за да добавите нов адрес</p>
                                </div>
                            </div>

                            <p class="text-xs text-gray-500 mt-2">
                                Вашият текущ IP: <strong>{{ current_ip }}</strong>
                                <span id="current-ip-status" class="ml-2"></span>
                            </p>

                            <!-- Скрити полета за съхранение на IP адресите -->
                            <input type="hidden" id="ip_whitelist" name="ip_whitelist" value="{{ ip_whitelist|e('html_attr') }}">
                            <input type="hidden" id="ip_blacklist" name="ip_blacklist" value="{{ ip_blacklist|e('html_attr') }}">
                            {# <!-- Backward compatibility -->
                            <input type="hidden" id="allowed_ips_text" name="allowed_ips_text" value="{{ ip_whitelist ?? '' }}"> #}
                        </div>

                        <div class="flex space-x-2 mt-6">
                            <button type="button"
                                    id="add-current-ip-btn"
                                    onclick="SecuritySettings.addCurrentIPToWhitelist()"
                                    class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors{% if current_ip_in_whitelist %}{{' '}}hidden{% endif %}">
                                <i class="ri-add-line mr-2"></i>
                                Добави текущия IP
                            </button>
                            {# {% if current_ip_in_whitelist %}
                                <div class="px-4 py-2 bg-gray-100 text-gray-600 rounded border">
                                    <i class="ri-check-line mr-2"></i>
                                    Текущият IP ({{ current_ip }}) е вече добавен
                                </div>
                            {% endif %} #}
                        </div>
                    </div>

                    <!-- IP Blacklist секция -->
                    <div class="mt-8">
                        <div class="flex justify-between items-center mb-3">
                            <label class="block text-sm font-medium text-gray-700">
                                Блокирани IP адреси (Blacklist)
                            </label>
                            <button type="button" id="add-blacklist-ip" class="px-3 py-1 bg-red-500 text-white text-sm rounded hover:bg-red-600">
                                <i class="ri-add-line mr-1"></i>Добави в Blacklist
                            </button>
                        </div>

                        <div class="border border-gray-300 rounded">
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP Адрес</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Описание</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Статус</th>
                                        <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Действия</th>
                                    </tr>
                                </thead>
                                <tbody id="blacklist-ip-addresses-table" class="bg-white divide-y divide-gray-200">
                                    <!-- Blacklist IP адресите се зареждат от новите таблици -->
                                    {% if blacklist_ips and blacklist_total > 0 %}
                                        {% for ip_data in blacklist_ips %}
                                            {% if ip_data.ip_address and ip_data.ip_address != '[]' %}
                                                {% set status_class = ip_data.status == 1 ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-600' %}
                                                {% set status_text = ip_data.status == 1 ? 'Блокиран' : 'Неактивен' %}
                                                {% set description = ip_data.description ? ip_data.description : 'Без описание' %}

                                                <tr class="hover:bg-gray-50" data-ip="{{ ip_data.ip_address }}" data-ip-id="{{ ip_data.id }}">
                                                    <td class="px-4 py-3 text-sm font-mono">{{ ip_data.ip_address }}</td>
                                                    <td class="px-4 py-3 text-sm text-gray-600">{{ description }}</td>
                                                    <td class="px-4 py-3">
                                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{' '}}{{ status_class }}">
                                                            {{ status_text }}
                                                        </span>
                                                    </td>
                                                    <td class="px-4 py-3 text-right">
                                                        <div class="flex justify-end space-x-1">
                                                            <button class="delete-blacklist-ip p-1 text-gray-400 hover:text-red-500"
                                                                    data-ip="{{ ip_data.ip_address }}" title="Изтрий">
                                                                <i class="ri-delete-bin-line"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            {% endif %}
                                        {% endfor %}
                                    {% endif %}
                                </tbody>
                            </table>

                            <!-- Blacklist Pagination -->
                            <div id="blacklist-pagination" class="{% if blacklist_total <= 20 %}{{' '}}hidden{% endif %} flex items-center justify-between px-4 py-3 bg-gray-50 border-t border-gray-200">
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm text-gray-700">Показване на</span>
                                    <select id="blacklist-per-page" class="border border-gray-300 rounded px-2 py-1 text-sm">
                                        <option value="10">10</option>
                                        <option value="20" selected>20</option>
                                        <option value="50">50</option>
                                        <option value="100">100</option>
                                    </select>
                                    <span class="text-sm text-gray-700">от <span id="blacklist-total">{{ blacklist_total ?? 0 }}</span> записа</span>
                                </div>

                                <div class="flex items-center space-x-1">
                                    <button id="blacklist-first-page" class="px-2 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed">
                                        <i class="ri-skip-back-line"></i>
                                    </button>
                                    <button id="blacklist-prev-page" class="px-2 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed">
                                        <i class="ri-arrow-left-s-line"></i>
                                    </button>
                                    <span class="px-3 py-1 text-sm">
                                        Страница <span id="blacklist-current-page">1</span> от <span id="blacklist-total-pages">1</span>
                                    </span>
                                    <button id="blacklist-next-page" class="px-2 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed">
                                        <i class="ri-arrow-right-s-line"></i>
                                    </button>
                                    <button id="blacklist-last-page" class="px-2 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed">
                                        <i class="ri-skip-forward-line"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Blacklist Simple Counter -->
                            <div id="blacklist-simple-counter" class="{% if blacklist_total > 20 %}{{' '}}hidden{% endif %} px-4 py-3 bg-gray-50 border-t border-gray-200 text-center">
                                <span class="text-sm text-gray-700">Общо <span id="blacklist-total-simple">{{ blacklist_total ?? 0 }}</span> записа</span>
                            </div>

                            <div id="blacklist-no-ip-addresses" class="text-center py-8 text-gray-500 {% if blacklist_total > 0 %}{{' '}}hidden{% endif %}">
                                <i class="ri-information-line text-2xl mb-2"></i>
                                <p>Няма блокирани IP адреси</p>
                                <p class="text-xs">Кликнете "Добави в Blacklist" за да добавите блокиран адрес</p>
                            </div>
                        </div>

                        <p class="text-xs text-gray-500 mt-2">
                            Блокираните IP адреси няма да имат достъп до административния панел
                        </p>
                    </div>
                </div>
            </div>


            <!-- Logging -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-file-list-line mr-2"></i>
                    Логиране
                </h2>
                
                <div class="space-y-4">
                    <div class="flex items-center space-x-3">
                        <input type="checkbox"
                               id="admin_action_logging"
                               name="admin_action_logging"
                               class="toggle-switch"
                               {{' '}}{{ admin_action_logging ? 'checked' : '' }}>
                        <div>
                            <label for="admin_action_logging" class="text-sm font-medium text-gray-700">
                                Лог на активността на администраторите
                            </label>
                            <p class="text-xs text-gray-500">Записва всички действия на администраторите</p>
                        </div>
                    </div>

                    <div class="flex items-center space-x-3">
                        <input type="checkbox"
                               id="failed_login_logging"
                               name="failed_login_logging"
                               class="toggle-switch"
                               {{' '}}{{ failed_login_logging ? 'checked' : '' }}>
                        <div>
                            <label for="failed_login_logging" class="text-sm font-medium text-gray-700">
                                Лог на неуспешни опити за вход
                            </label>
                            <p class="text-xs text-gray-500">Записва всички неуспешни опити за вход</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Password Policy Settings -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-shield-check-line mr-2"></i>
                    Политика за пароли
                </h2>

                <div class="space-y-6">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Минимална дължина
                            </label>
                            <input type="number"
                                   name="password_min_length"
                                   class="w-full px-3 py-2 border border-gray-300 rounded"
                                   value="{{ password_min_length ?? 8 }}"
                                   min="6"
                                   max="32">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Изтичане (дни)
                            </label>
                            <input type="number"
                                   name="password_expiry_days"
                                   class="w-full px-3 py-2 border border-gray-300 rounded"
                                   value="{{ password_expiry_days ?? 90 }}"
                                   min="30"
                                   max="365">
                        </div>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div class="flex items-center justify-start space-x-2">
                            <input type="checkbox"
                                   name="password_require_uppercase"
                                   class="toggle-switch"
                                   {{ password_require_uppercase ? 'checked' : '' }}>
                            <span class="text-sm text-gray-700">Изисква главни букви</span>
                        </div>

                        <div class="flex items-center justify-start space-x-2">
                            <input type="checkbox"
                                   name="password_require_lowercase"
                                   class="toggle-switch"
                                   {{ password_require_lowercase ? 'checked' : '' }}>
                            <span class="text-sm text-gray-700">Изисква малки букви</span>
                        </div>

                        <div class="flex items-center justify-start space-x-2">
                            <input type="checkbox"
                                   name="password_require_numbers"
                                   class="toggle-switch"
                                   {{ password_require_numbers ? 'checked' : '' }}>
                            <span class="text-sm text-gray-700">Изисква цифри</span>
                        </div>

                        <div class="flex items-center justify-start space-x-2">
                            <input type="checkbox"
                                   name="password_require_symbols"
                                   class="toggle-switch"
                                   {{ password_require_symbols ? 'checked' : '' }}>
                            <span class="text-sm text-gray-700">Изисква символи</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Login Security Settings -->
            <div class="bg-white rounded shadow p-6 mt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="ri-lock-line mr-2"></i>
                    Сигурност при вход
                </h2>

                <div class="space-y-6">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Максимални опити за вход
                            </label>
                            <input type="number"
                                   name="max_login_attempts"
                                   class="w-full px-3 py-2 border border-gray-300 rounded"
                                   value="{{ max_login_attempts ?? 5 }}"
                                   min="3"
                                   max="20">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Време за заключване (минути)
                            </label>
                            <input type="number"
                                   name="lockout_duration"
                                   class="w-full px-3 py-2 border border-gray-300 rounded"
                                   value="{{ lockout_duration ?? 30 }}"
                                   min="5"
                                   max="1440">
                        </div>
                    </div>

                    <div class="grid grid-cols-1 gap-4">
                        <div class="flex items-center justify-start space-x-2">
                            <input type="checkbox"
                                   name="login_notifications"
                                   class="toggle-switch"
                                   {{' '}}{{ login_notifications ? 'checked' : '' }}>
                            <span class="text-sm text-gray-700">Известия за вход</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>

    <!-- Save Button -->
    <div class="flex justify-end mt-6">
        <button type="button" id="save-security-settings"
                class="px-6 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors whitespace-nowrap !rounded-button">
            <i class="ri-save-line mr-2"></i>
            Запази настройки
        </button>
        </div>
    </div>
</form>

<!-- JavaScript конфигурация -->
<script>
// Глобална конфигурация за Security настройки
window.settingsSecurityConfig = {
    currentIP: '{{ current_ip }}',
};
</script>
