<?php

namespace Theme25\Delivery;

/**
 * Конфигурация за куриер BoxNow
 */
class Boxnow
{
    /**
     * Singleton инстанция на API клиента
     */
    private static $apiInstance = null;

    /**
     * Код на куриера
     */
    const CODE = 'boxnow';

    /**
     * Име на куриера
     */
    const NAME = 'BoxNow';

    /**
     * Получава singleton инстанция на BoxNow API
     *
     * @param array $settings Настройки за API (client_id, client_secret, api_url)
     * @return \Theme25\Delivery\Boxnow\API|null
     */
    private static function getApiInstance(array $settings = [])
    {
        if (self::$apiInstance === null && !empty($settings['client_id']) && !empty($settings['client_secret'])) {
            try {
                self::$apiInstance = new \Theme25\Delivery\Boxnow\API(
                    $settings['client_id'],
                    $settings['client_secret'],
                    $settings['api_url'] ?? 'https://api.boxnow.gr'
                );
            } catch (Exception $e) {
                error_log("Грешка при инициализация на BoxNow API: " . $e->getMessage());
                F()->log->developer("Грешка при инициализация на BoxNow API: " . $e->getMessage(), __FILE__, __LINE__);
                return null;
            }
        }

        return self::$apiInstance;
    }

    /**
     * Тества връзката с BoxNow API
     *
     * @param array $settings Настройки за API (client_id, client_secret, api_url и др.)
     * @return array Резултат от тестването
     */
    public static function testConnection(array $settings = []) {


        F()->log->developer($settings, __FILE__, __LINE__);

        try {
            // Валидация на задължителните параметри
            if (empty($settings['client_id'])) {
                return ['error' => 'Липсва Client ID за BoxNow API'];
            }

            if (empty($settings['client_secret'])) {
                return ['error' => 'Липсва Client Secret за BoxNow API'];
            }

            // Подготовка на настройките за API
            $apiSettings = [
                'client_id' => $settings['client_id'],
                'client_secret' => $settings['client_secret'],
                'api_url' => $settings['api_url'] ?? 'https://api.boxnow.gr',
                'timeout' => (int)($settings['timeout'] ?? 30)
            ];

            // Получаване на API инстанция
            $api = self::getApiInstance($apiSettings);

            if (!$api) {
                return ['error' => 'Неуспешно създаване на BoxNow API инстанция'];
            }

            // Проверка дали API класът има authenticate() метод
            if (!is_callable([$api, 'authenticate'])) {
                return ['error' => 'BoxNow API класът не поддържа authenticate() метод'];
            }

            // Извикване на authenticate() за тестване на връзката
            $authResult = $api->authenticate();

            F()->log->developer($authResult, __FILE__, __LINE__);

            // Проверка на резултата
            if ($authResult === false || $authResult === null) {
                return ['error' => 'Неуспешно извикване на authenticate() - проверете Client ID и Client Secret'];
            }

            // Ако резултатът е масив, проверяваме дали има access_token
            if (is_array($authResult)) {
                if (empty($authResult['access_token'])) {
                    return [
                        'error' => 'Неуспешна автентикация - липсва access token',
                        'data' => $authResult
                    ];
                } else {
                    return [
                        'success' => true,
                        'message' => 'Връзката с BoxNow API е успешна.',
                        'data' => ['token_type' => $authResult['token_type'] ?? 'Bearer']
                    ];
                }
            }

            // Ако резултатът не е масив, но не е false/null, считаме го за успешен
            return [
                'success' => true,
                'message' => 'Връзката с BoxNow API е успешна',
                'data' => ['response_type' => gettype($authResult)]
            ];

        } catch (\Exception $e) {
            // Логиране на грешката
            error_log('BoxNow API connection test error: ' . $e->getMessage());
            F()->log->developer("Грешка при тестване на BoxNow API: " . $e->getMessage(), __FILE__, __LINE__);

            // Анализ на типа грешка за по-информативно съобщение
            $errorMessage = $e->getMessage();

            if (strpos($errorMessage, 'authentication') !== false || strpos($errorMessage, 'unauthorized') !== false) {
                return ['error' => 'Грешка при автентикация - проверете Client ID и Client Secret'];
            } elseif (strpos($errorMessage, 'connection') !== false || strpos($errorMessage, 'timeout') !== false) {
                return ['error' => 'Грешка при свързване с BoxNow API сървъра - проверете мрежовата връзка'];
            } elseif (strpos($errorMessage, 'curl') !== false) {
                return ['error' => 'Грешка в мрежовата заявка - проверете настройките на сървъра'];
            } else {
                return ['error' => 'Грешка при тестване на BoxNow API: ' . $errorMessage];
            }
        }
    }

    /**
     * Получава настройките за конфигуриране на куриера
     *
     * @return array
     */
    public static function getConfigFields()
    {
        return [
            // TAB: Основни настройки
            'main_settings_group' => [
                'type' => 'group',
                'label' => 'Основни настройки',
                'subfields' => [
                    'api_url' => [
                        'type' => 'text',
                        'label' => 'API URL',
                        'required' => true,
                        'connection_required' => true
                    ],
                    'client_id' => [
                        'type' => 'text',
                        'label' => 'Client ID',
                        'required' => true,
                        'connection_required' => true
                    ],
                    'client_secret' => [
                        'type' => 'password',
                        'label' => 'Client Secret',
                        'required' => true,
                        'connection_required' => true
                    ],
                    'warehouse_number' => [
                        'type' => 'text',
                        'label' => 'Номер на склад',
                    ],
                    'partner_id' => [
                        'type' => 'text',
                        'label' => 'Partner ID',
                        'required' => true
                    ],
                    'test_account' => [
                        'type' => 'button',
                        'class' => 'btn btn-primary',
                        'label' => 'Тест на връзката',
                        'icon' => '<i class="ri-wifi-line mr-2"></i>',
                        'javascript' => 'testBoxnowConnection'
                    ],
                    'test_result' => [
                        'type' => 'custom',
                        'id' => 'test-result',
                        'class' => 'hidden',
                        'label' => 'Резултат от теста',
                        'content' => ''
                    ]
                ],
            ],

            'pricing' => [
                'type' => 'select_group',
                'label' => 'Ценообразуване',
                'options' => [
                    'weight_based' => 'Базирано на тегло',
                    'fixed' => 'Фиксирана цена',
                    'free' => 'Безплатна доставка',
                ],
                'help' => 'Изберете как да се формира цената за доставка.',
                'conditions' => [
                    'weight_based' => ['show' => ['weight_value']],
                    'fixed' => ['show' => ['fixed_price']],
                    'free' => ['show' => ['free_shipping_total']],
                ],
                'subfields' => [
                    'weight_value' => [
                        'type' => 'custom',
                        'label' => 'Ценообразуване по тегло',
                        'source' => 'Theme25\Delivery\Boxnow::getWeightPricingTable'
                    ],
                    'fixed_price' => [
                        'type' => 'text',
                        'label' => 'Фиксирана цена за доставка'
                    ],
                    'free_shipping_total' => [
                        'type' => 'text',
                        'label' => 'Безплатна доставка над стойност'
                    ]
                ]
            ],

            'tax_class_id' => [
                // 'type' => 'select',
                // 'label' => 'Данъчен клас',
                // 'options_source' => 'Theme25\Delivery\Boxnow::getTaxClasses|select'
                'type' => 'hidden',
                'value' => 0
            ],

            'geo_zone_id' => [
                // 'type' => 'select',
                // 'label' => 'Географска зона',
                // 'options_source' => 'Theme25\Delivery\Boxnow::getGeoZones|select'
                'type' => 'hidden',
                'value' => 0
            ],

            'payment_modules' => [
                'type' => 'checkbox_group',
                'label' => 'Разрешени методи за плащане',
                'options_source' => 'Theme25\Delivery\Boxnow::getPaymentMethods',
                'select_deselect_all' => true,
                'help' => 'Изберете кои методи за плащане са разрешени за BoxNow доставка'
            ],

            'order_status_id' => [
                'type' => 'select',
                'label' => 'Статус на поръчката след генериране на заявката:',
                'options_source' => 'Theme25\Delivery\Boxnow::getOrderStatuses|select'
            ],

            'sort_order' => [
                'type' => 'text',
                'label' => 'Ред на сортиране',
                'default_value' => 0
            ],

            'status' => [
                'type' => 'toggle',
                'label' => 'Статус',
                'options' => [
                    0 => 'Неактивен',
                    1 => 'Активен',
                ]
            ],
        ];
    }

    /**
     * Получава стандартните настройки за куриера
     *
     * @return array
     */
    public static function getDefaultSettings()
    {
        return [
            'api_url' => 'https://api.boxnow.gr',
            'client_id' => '',
            'client_secret' => '',
            'warehouse_number' => '',
            'partner_id' => '',
            'pricing' => 'weight_based',
            'weight_value' => [
                ['from' => 0, 'to' => 1, 'price' => 5.00],
                ['from' => 1, 'to' => 3, 'price' => 7.00],
                ['from' => 3, 'to' => 5, 'price' => 9.00]
            ],
            'fixed_price' => 6.00,
            'free_shipping_total' => 100.00,
            'tax_class_id' => 0,
            'geo_zone_id' => 0,
            'payment_modules' => [],
            'order_status_id' => 3,
            'sort_order' => 0,
            'status' => 1,
        ];
    }

    /**
     * Валидира настройките на куриера
     *
     * @param array $settings
     * @return array Масив с грешки (празен ако няма грешки)
     */
    public static function validateSettings($settings)
    {
        $errors = [];

        if (empty($settings['api_url'])) {
            $errors[] = 'API URL е задължително поле';
        }

        if (empty($settings['client_id'])) {
            $errors[] = 'Client ID е задължително поле';
        }

        if (empty($settings['client_secret'])) {
            $errors[] = 'Client Secret е задължително поле';
        }

        if (empty($settings['partner_id'])) {
            $errors[] = 'Partner ID е задължително поле';
        }

        return $errors;
    }

    /**
     * Проверява дали са попълнени всички полета необходими за тестване на връзката
     *
     * @param array $settings
     * @return bool
     */
    public static function canTestConnection($settings)
    {
        $requiredFields = ['api_url', 'client_id', 'client_secret'];

        foreach ($requiredFields as $field) {
            if (empty($settings[$field])) {
                return false;
            }
        }

        return true;
    }


    /**
     * Получава HTML за таблица с ценообразуване по тегло
     *
     * @param array $current_settings
     * @return string
     */
    public static function getWeightPricingTable(array $current_settings = [])
    {
        $data = [
            'weight_value' => $current_settings['weight_value'] ? $current_settings['weight_value'] : []
        ];

        return CM()->renderPartialTemplate('setting/delivery/boxnow_weight_table', $data, $controllerPath = 'Backend');
    }

    /**
     * Получава данъчните класове
     *
     * @return array
     */
    public static function getTaxClasses()
    {
        CM()->loadModelAs('localisation/tax_class', 'taxClassModel');
        $taxClasses = CM()->taxClassModel->getTaxClasses();

        if (empty($taxClasses)) {
            return [];
        }

        $_taxClasses = [0 => 'Без данък'];
        foreach ($taxClasses as $taxClass) {
            $_taxClasses[$taxClass['tax_class_id']] = $taxClass['title'];
        }

        return $_taxClasses;
    }

    /**
     * Получава географските зони
     *
     * @return array
     */
    public static function getGeoZones()
    {
        CM()->loadModelAs('localisation/geo_zone', 'geoZoneModel');
        $geoZones = CM()->geoZoneModel->getGeoZones();

        if (empty($geoZones)) {
            return [];
        }

        $_geoZones = [0 => 'Всички зони'];
        foreach ($geoZones as $geoZone) {
            $_geoZones[$geoZone['geo_zone_id']] = $geoZone['name'];
        }

        return $_geoZones;
    }

    /**
     * Получава методите за плащане
     *
     * @return array
     */
    public static function getPaymentMethods()
    {
        CM()->loadModelAs('setting/extension', 'extensionModel');
        $paymentMethods = CM()->extensionModel->getInstalled('payment');

        if (empty($paymentMethods)) {
            return [];
        }

        $paymentTitles = [
            'bank_transfer' => 'Банков превод',
            'cod' => 'Наложен платеж',
            'epay' => 'ePay',   
            'free_checkout' => 'Безплатна доставка',
            'pp_standard' => 'PayPal',
            'ubb_vpos' => 'UBB VPOS - банкови карти'
        ];

        $_paymentMethods = [];
        foreach ($paymentMethods as $method) {

            $status = CM()->getConfigFromSecondDB('payment_'.$method . '_status', 0, 'payment');

            if(!$status) continue;

            if (isset($paymentTitles[$method])) {
                $_paymentMethods[$method] = $paymentTitles[$method];
                continue;
            }
            
            $title = CM()->getLanguageText('heading_title','extension/payment/' . $method);

            if ($title === 'heading_title') {
                $title = ucfirst($method);
            }

            $_paymentMethods[$method] = $title;
        }

        return $_paymentMethods;
    }

    /**
     * Получава статусите на поръчките
     *
     * @return array
     */
    public static function getOrderStatuses()
    {
        CM()->loadModelAs('localisation/order_status', 'orderStatusModel');
        $orderStatuses = CM()->orderStatusModel->getOrderStatuses();

        if (empty($orderStatuses)) {
            return [];
        }

        $_orderStatuses = [];
        foreach ($orderStatuses as $orderStatus) {
            $_orderStatuses[$orderStatus['order_status_id']] = $orderStatus['name'];
        }

        return $_orderStatuses;
    }
}
